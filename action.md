
我们选择的方案是：**在项目中增加一个支持管理员登录后添加任务的页面。**

这个方案的优点是：
*   **用户友好：** 管理员可以通过熟悉的界面操作，无需直接与数据库交互。
*   **安全性：** 可以通过权限控制，确保只有管理员才能访问和使用这个功能。
*   **可扩展性：** 未来可以在这个页面基础上，扩展任务的编辑、删除、审核等更多管理功能。

---

### 阶段一：核心体验强化与沉浸式基础

#### 4. 后端任务管理界面 (管理员创建任务)

**改动点概述：**

1.  **后端 `backend/src/routes/task.routes.js`：** 严格限制 `createTask` 接口为 `admin` 角色。
2.  **后端 `backend/src/controllers/task.controller.js`：** （无需修改，`createTask` 逻辑已存在）
3.  **前端 `frontend/src/stores/task.js`：** 新增 `createTask` action，用于调用后端创建任务接口。
4.  **前端 `frontend/src/router/index.js`：** 添加新的路由 `/admin/tasks/create`，并限制只有管理员可访问。
5.  **前端 `frontend/src/views/AdminTaskCreate.vue`：** **新增**管理员任务创建页面组件，包含任务创建表单。
6.  **前端 `frontend/src/components/AppHeader.vue`：** 为管理员添加一个在导航栏中可见的“任务管理”或“发布榜文”入口。
7.  **前端 `frontend/src/styles/ancient-pixel.scss`：** （可选，如果需要通用管理页面样式可在此添加）

---

**详细代码修改：**

### **1. 后端修改**

**1.1. `backend/src/routes/task.routes.js` (修改文件)**

将 `createTask` 接口的权限从 `['admin', 'user']` 严格限制为 `['admin']`。

```javascript
// File path: backend/src/routes/task.routes.js
/**
 * 任务相关路由
 * 处理任务的创建、查询、领取、完成、删除等请求
 */

const express = require('express');
const router = express.Router();
const taskController = require('../controllers/task.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// 所有任务相关的路由都需要身份验证
router.use(authMiddleware.protect);

// 创建任务：**仅允许管理员创建**
router.post('/', authMiddleware.restrictTo('admin'), taskController.createTask); // **修改权限为 'admin'**

// 获取所有任务列表
// 对于非管理员用户，getAllTasks 控制器内部会默认只返回 public 任务
router.get('/', taskController.getAllTasks);

// 获取单个任务详情
router.get('/:taskId', taskController.getTaskById);

// 领取任务
router.post('/:taskId/claim', taskController.claimTask);

// 完成任务
router.post('/:taskId/complete', taskController.completeTask);

// 更新任务 (创建者或管理员)
router.put('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.updateTask);

// 删除任务 (创建者或管理员)
router.delete('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.deleteTask);

module.exports = router;
```

### **2. 前端修改**

**2.1. `frontend/src/stores/task.js` (修改文件)**

新增 `createTask` action。

```javascript
// File path: frontend/src/stores/task.js
import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [],
    loading: false,
    error: null,
    selectedTask: null,
    loadingTaskDetail: false,
    taskDetailError: null,
    submittingAction: false,
    actionError: null,
    actionSuccessMessage: null,
    // **新增：任务创建相关状态**
    creatingTask: false,
    createTaskError: null,
    createTaskSuccess: false,
  }),

  getters: {
    // ... (现有 getters 保持不变)
    getTaskStatusText: () => (status) => { /* ... */ },
    getTaskStatusClass: () => (status) => { /* ... */ },
    getTaskActionButtonText: (state) => (task) => { /* ... */ },
    isTaskActionButtonDisabled: (state) => (task) => { /* ... */ }
  },

  actions: {
    // 获取任务列表
    async fetchTasks(filters = {}) { /* ... */ },

    // 获取单个任务详情
    async fetchTaskById(taskId) { /* ... */ },

    // 领取任务
    async claimTask(taskId) { /* ... */ },

    // 完成任务
    async completeTask(taskId) { /* ... */ },

    // 清除选中的任务详情
    clearSelectedTask() { /* ... */ },

    // **新增：创建任务 Action**
    async createTask(taskData) {
      this.creatingTask = true;
      this.createTaskError = null;
      this.createTaskSuccess = false;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.createTaskError = '侠士尚未登录，无法发布榜文。'; // 理论上这个接口只会被管理员访问，但加上防御性检查
        this.creatingTask = false;
        return { success: false, message: this.createTaskError };
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks`, taskData, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.createTaskSuccess = true;
          // 成功后可以考虑刷新任务列表
          await this.fetchTasks();
          console.log('Store: 任务创建成功:', response.data.task.title);
          return { success: true, message: response.data.message || '榜文发布成功！' };
        } else {
          this.createTaskError = response.data.message || '发布榜文失败。';
          console.error('Store: 发布榜文失败:', this.createTaskError);
          return { success: false, message: this.createTaskError };
        }
      } catch (err) {
        console.error('Store: 发布榜文请求错误:', err);
        this.createTaskError = err.response?.data?.message || '发布榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.createTaskError };
      } finally {
        this.creatingTask = false;
      }
    },
    // **新增：重置任务创建状态**
    resetCreateTaskState() {
      this.createTaskError = null;
      this.createTaskSuccess = false;
    }
  },
});
```

**2.2. `frontend/src/router/index.js` (修改文件)**

新增管理员任务创建页面的路由，并设置 `meta.requiresAdmin: true`。

```javascript
// File path: frontend/src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user' // **新增：导入 userStore**

// 导入组件
const Login = () => import('../views/Login.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const Workspace = () => import('../views/Workspace.vue')
const Tasks = () => import('../views/Tasks.vue')
const Attendance = () => import('../views/Attendance.vue')
const Community = () => import('../views/Community.vue')
const Store = () => import('../views/Store.vue')
// **新增：导入管理员任务创建组件**
const AdminTaskCreate = () => import('../views/AdminTaskCreate.vue')


// 定义路由
const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/workspace',
    name: 'Workspace',
    component: Workspace,
    meta: { requiresAuth: true }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: Tasks,
    meta: { requiresAuth: true }
  },
  {
    path: '/attendance',
    name: 'Attendance',
    component: Attendance,
    meta: { requiresAuth: true }
  },
  {
    path: '/community',
    name: 'Community',
    component: Community,
    meta: { requiresAuth: true }
  },
  {
    path: '/store',
    name: 'Store',
    component: Store,
    meta: { requiresAuth: true }
  },
  // **新增：管理员任务创建页面**
  {
    path: '/admin/tasks/create',
    name: 'AdminTaskCreate',
    component: AdminTaskCreate,
    meta: { requiresAuth: true, requiresAdmin: true } // 需要登录，且需要管理员权限
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore(); // 获取 userStore 实例
  
  // 确保用户信息已加载 (如果未加载则尝试加载)
  if (!userStore.userInfo && userStore.token && !userStore.loading) {
    await userStore.fetchUserInfo();
  }

  const isAuthenticated = userStore.isLoggedIn; // 从 store 的 getter 获取登录状态
  const userRole = userStore.userInfo?.role; // 获取用户角色

  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要登录但未登录，重定向到登录页
    next({ name: 'Login' });
  } else if (to.meta.requiresAdmin && userRole !== 'admin') {
    // 需要管理员权限但不是管理员，重定向到 Dashboard 或显示无权限提示
    alert('权限不足，需要管理员身份才能访问此页面！');
    next({ name: 'Dashboard' }); // 重定向到 Dashboard
  }
  else {
    // 已登录或不需要登录的页面，或已认证的管理员
    next();
  }
})

export default router
```

**2.3. `frontend/src/views/AdminTaskCreate.vue` (新增文件)**

这是管理员用于创建任务的页面组件。

```vue
<template>
  <div class="admin-task-create-page ancient-pixel-container">
    <h1 class="page-title">发布榜文 · 掌门任务</h1>

    <div class="card create-task-card">
      <h2 class="card-title">📖 新增江湖榜文</h2>
      <form @submit.prevent="submitTask">
        <div class="form-group">
          <label for="title">榜文标题:</label>
          <input type="text" id="title" v-model="newTask.title" class="pixel-input" required maxlength="100" />
        </div>

        <div class="form-group">
          <label for="description">榜文描述:</label>
          <textarea id="description" v-model="newTask.description" rows="5" class="pixel-input" required maxlength="500"></textarea>
        </div>

        <div class="form-group">
          <label for="rewardPoints">奖励内力值:</label>
          <input type="number" id="rewardPoints" v-model.number="newTask.rewardPoints" class="pixel-input" required min="10" />
        </div>

        <div class="form-group">
          <label for="category">任务分类:</label>
          <select id="category" v-model="newTask.category" class="pixel-input">
            <option value="work">工作 (💼)</option>
            <option value="study">学习 (📚)</option>
            <option value="design">设计 (🎨)</option>
            <option value="development">开发 (💻)</option>
            <option value="document">文档 (📄)</option>
            <option value="other">其他 (✨)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="type">任务类型:</label>
          <select id="type" v-model="newTask.type" class="pixel-input">
            <option value="official">官方榜文</option>
            <option value="personal">个人挑战 (需指定领取者)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="priority">优先级:</label>
          <select id="priority" v-model="newTask.priority" class="pixel-input">
            <option value="low">低</option>
            <option value="medium">中</option>
            <option value="high">高</option>
          </select>
        </div>

        <div class="form-group">
          <label for="dueDate">截止日期:</label>
          <input type="date" id="dueDate" v-model="newTask.dueDate" class="pixel-input" />
        </div>

        <div class="form-group">
          <label class="pixel-checkbox">
            <input type="checkbox" v-model="newTask.isPublic">
            <span>是否公开 (公开榜文所有人可见并可领取)</span>
          </label>
        </div>

        <div v-if="createTaskError" class="error-message shaking-pixel">{{ createTaskError }}</div>
        <div v-if="createTaskSuccess" class="success-message">榜文发布成功！</div>

        <button type="submit" class="pixel-button primary" :disabled="creatingTask">
          {{ creatingTask ? '榜文发布中...' : '🎉 立即发布榜文' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useTaskStore } from '@/stores/task';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';

const taskStore = useTaskStore();
const router = useRouter();

const { creatingTask, createTaskError, createTaskSuccess } = storeToRefs(taskStore);

const newTask = ref({
  title: '',
  description: '',
  rewardPoints: 10,
  category: 'work',
  type: 'official',
  priority: 'medium',
  dueDate: null, // YYYY-MM-DD
  isPublic: true,
  tags: [] // 暂时不提供标签输入，可后续扩展
});

// 重置表单
const resetForm = () => {
  newTask.value = {
    title: '',
    description: '',
    rewardPoints: 10,
    category: 'work',
    type: 'official',
    priority: 'medium',
    dueDate: null,
    isPublic: true,
    tags: []
  };
  taskStore.resetCreateTaskState(); // 清除 store 中的成功/失败状态
};

const submitTask = async () => {
  // 将日期格式化为 ISO 字符串以便后端处理
  const taskDataToSend = { ...newTask.value };
  if (taskDataToSend.dueDate) {
    taskDataToSend.dueDate = new Date(taskDataToSend.dueDate).toISOString();
  }

  const result = await taskStore.createTask(taskDataToSend);
  if (result.success) {
    console.log('榜文发布成功！');
    resetForm(); // 发布成功后重置表单
    // 可以在这里跳转到任务列表，或者在页面上显示成功信息
    // router.push('/tasks');
  } else {
    console.error('榜文发布失败:', result.message);
  }
};

onMounted(() => {
  resetForm(); // 页面加载时重置表单状态
});
</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.admin-task-create-page {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  background-color: var(--color-ancient-paper);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
  align-self: flex-start; // 左对齐
}

.create-task-card {
  @extend .ancient-pixel-container; // 继承通用卡片样式
  width: 100%;
  max-width: 700px; // 适应表单内容
  padding: 30px;

  .card-title {
    font-size: 1.5em;
    color: var(--color-ancient-ink);
    margin-bottom: 25px;
    text-align: center;
    border-bottom: 2px dashed var(--color-ancient-light-brown);
    padding-bottom: 10px;
    font-family: 'ZCOOL KuaiLe', serif;
  }

  .form-group {
    margin-bottom: 20px;
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: var(--color-ancient-dark-brown);
      font-family: 'Pixelify Sans', monospace;
      font-size: 1.1em;
    }
    input[type="text"],
    input[type="number"],
    input[type="date"],
    textarea,
    select {
      @extend .pixel-input; // 继承像素输入框样式
      width: 100%;
    }

    textarea {
      resize: vertical;
    }
    select {
      appearance: none; // 移除默认下拉箭头
      background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20fill%3D%22%23777%22%20d%3D%22M5%206l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E');
      background-repeat: no-repeat;
      background-position: right 10px top 50%;
      background-size: 12px auto;
      padding-right: 30px;
    }
  }

  .pixel-checkbox {
    margin-top: 10px;
    span {
      font-size: 1em;
    }
  }

  .error-message, .success-message {
    @extend .error-message; // 继承样式
    margin-top: 10px;
    margin-bottom: 20px; // 保持与其他表单元素的间距
  }
  .success-message {
    border-color: var(--color-ancient-jade);
    color: var(--color-ancient-jade-dark);
    background-color: var(--color-ancient-paper);
  }

  .pixel-button {
    @extend .pixel-button; // 继承按钮样式
    width: 100%;
    margin-top: 20px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-task-create-page {
    padding: 15px;
  }
  .page-title {
    font-size: 1.8em;
    text-align: center;
    align-self: center;
  }
  .create-task-card {
    padding: 20px;
  }
  .create-task-card .card-title {
    font-size: 1.3em;
  }
  .create-task-card .form-group label {
    font-size: 1em;
  }
  .create-task-card .pixel-input {
    font-size: 0.9em;
  }
  .create-task-card .pixel-button {
    font-size: 0.9em;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .admin-task-create-page {
    padding: 10px;
  }
  .page-title {
    font-size: 1.5em;
    margin-bottom: 15px;
  }
  .create-task-card {
    padding: 15px;
  }
  .create-task-card .card-title {
    font-size: 1.1em;
  }
  .create-task-card .form-group label {
    font-size: 0.9em;
  }
  .create-task-card .pixel-input {
    font-size: 0.85em;
    padding: 8px 10px;
  }
  .create-task-card .pixel-button {
    font-size: 0.8em;
    padding: 8px 15px;
  }
}
</style>
```

**2.4. `frontend/src/components/AppHeader.vue` (修改文件)**

为管理员角色添加一个导航链接。

```vue
<template>
  <header class="app-header">
    <div class="header-left">
      <!-- Logo Area -->
      <div class="logo" @click="goToDashboard">
        <span class="mascot-icon">🐉</span>
        <span class="logo-text">云端工位</span>
      </div>
    </div>
    <div class="header-center">
      <!-- Navigation Menu -->
      <nav class="main-nav">
        <ul>
          <li v-for="item in navItems" :key="item.name">
            <router-link :to="item.path" active-class="active-link" class="nav-link pixel-button-nav">
              <span class="nav-icon">{{ item.iconPlaceholder }}</span>
              <span class="nav-text">{{ item.name }}</span>
            </router-link>
          </li>
          <!-- **新增：管理员入口** -->
          <li v-if="userStore.userInfo?.role === 'admin'">
            <router-link to="/admin/tasks/create" active-class="active-link" class="nav-link pixel-button-nav">
              <span class="nav-icon">👑</span> <!-- Crown emoji for admin -->
              <span class="nav-text">发布榜文</span>
            </router-link>
          </li>
        </ul>
      </nav>
    </div>
    <div class="header-right">
      <!-- Function Area -->
      <div class="function-area">
        <!-- Message Notification -->
        <div class="notification-area">
          <span class="notification-icon">📜</span>
          <span class="notification-indicator"></span>
        </div>

        <!-- User Area -->
        <div class="user-area">
          <div class="user-dropdown" @click="toggleDropdown">
            <div class="user-avatar-small">
              <img :src="headerAvatarUrl" alt="用户头像" class="avatar-img pixelated-image">
            </div>
            <div class="dropdown-menu" v-if="isDropdownOpen">
              <ul>
                <li><router-link to="/workspace" class="ancient-text">我的工位</router-link></li>
                <li><a href="#" @click.prevent="logout" class="ancient-text">归隐</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
// File path: frontend/src/components/AppHeader.vue
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user'; // **确保已引入 userStore**

const router = useRouter();
const userStore = useUserStore(); // **初始化 userStore**

const userInfo = computed(() => userStore.userInfo);
const headerAvatarUrl = computed(() => userStore.fullAvatarUrl);

const navItems = ref([
  { name: '我的工位', path: '/dashboard', iconPlaceholder: '🏛️' },
  { name: '修行日志', path: '/attendance', iconPlaceholder: '🕰️' },
  { name: '榜文任务', path: '/tasks', iconPlaceholder: '⚔️' },
  { name: '江湖茶馆', path: '/community', iconPlaceholder: '🏮' },
  { name: '江湖宝库', path: '/store', iconPlaceholder: '💎' },
]);

onMounted(() => {
  if (!userStore.userInfo && userStore.token) { // 只有在没有用户信息但有 token 时才尝试获取
    userStore.fetchUserInfo();
  }
});

const logout = () => {
  userStore.logout();
  router.push({ name: 'Login' });
};

const goToDashboard = () => {
  router.push('/dashboard');
};

const isDropdownOpen = ref(false);
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};
</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss';

/* 基础头部样式 */
.app-header { /* ... 保持不变 ... */ }
.header-left, .header-center, .header-right { /* ... 保持不变 ... */ }
.header-left { /* ... 保持不变 ... */ }
.header-center { /* ... 保持不变 ... */ }
.header-right { /* ... 保持不变 ... */ }
.logo { /* ... 保持不变 ... */ }
.mascot-icon { /* ... 保持不变 ... */ }
.logo-text { /* ... 保持不变 ... */ }

/* 导航样式 */
.main-nav ul { /* ... 保持不变 ... */ }
.main-nav li { /* ... 保持不变 ... */ }
.nav-link { /* ... 保持不变 ... */
  &.pixel-button-nav {
    // 继承 base button styles from ancient-pixel.scss
    @extend .pixel-button-nav; // Ensures consistent styling
  }
}

.nav-link.active-link { /* ... 保持不变 ... */ }
.nav-icon { /* ... 保持不变 ... */ }

/* 功能区域样式 */
.function-area { /* ... 保持不变 ... */ }
.notification-area { /* ... 保持不变 ... */ }
.notification-icon { /* ... 保持不变 ... */ }
.notification-indicator { /* ... 保持不变 ... */ }

/* 用户区域样式 */
.user-area { /* ... 保持不变 ... */ }
.user-dropdown { /* ... 保持不变 ... */ }
.user-avatar-small { /* ... 保持不变 ... */ }
.avatar-img { /* ... 保持不变 ... */ }

/* 下拉菜单样式 */
.dropdown-menu { /* ... 保持不变 ... */ }
.dropdown-menu ul { /* ... 保持不变 ... */ }
.dropdown-menu li { /* ... 保持不变 ... */
  a, .router-link-active { // Target router-link as well
    // Ensure these also have ancient-text styling or inherit from ancient-pixel.scss
    font-family: 'Noto Serif SC', serif;
    color: var(--color-ancient-dark-brown);
  }
}

/* 响应式调整 */
@media (max-width: 768px) { /* ... 保持不变 ... */ }
@media (max-width: 480px) { /* ... 保持不变 ... */ }
</style>
```

---
