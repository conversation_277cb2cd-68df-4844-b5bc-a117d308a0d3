{"name": "my-monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "npm run dev --workspace frontend & npm run dev --workspace backend"}, "keywords": [], "author": "", "license": "ISC", "workspaces": ["backend", "frontend"], "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "pinia": "^3.0.2", "vue-router": "^4.5.1"}, "devDependencies": {"nodemon": "^3.1.10"}}