# 云端工位 - 像素武侠版 Web 应用项目

## 项目目标

创建一个具有独特像素武侠风格的 Web 应用，模拟"江湖工位"的场景，提供打卡修行、榜文任务、武林秘闻和门派交流等功能，旨在为用户提供一个轻松有趣的线上"江湖修行"平台。

## 核心功能模块

*   **用户系统：** 登录、注册、忘记密码、记住密码、第三方登录。
*   **我的工位 (Dashboard)：** 用户登录后的**核心概览页**。提供今日关键数据（积分、声望）、今日打卡状态和**快速打卡按钮**，以及今日待办/任务预览和最新公告。
*   **打卡中心：** 考勤管理和**数据回顾中心**。提供详细的打卡历史记录、统计分析、日历视图等功能，帮助用户深入了解自己的"修行"情况。此页面**不再提供直接的打卡操作**。
*   **任务大厅：** 浏览、领取、完成和发布任务。
*   **江湖茶馆：** 用户交流互动，发布榜文和评论。
*   **江湖宝库 (待规划)：** 使用积分兑换虚拟或实物商品。
*   **个人洞府：** 个人信息修改、头像上传等。

## 技术栈

*   **前端：** Vue.js 3 + Vite + SCSS + Pinia
*   **后端：** Node.js + Express.js
*   **数据库：** MongoDB
*   **API 风格：** RESTful API

## 整体架构

采用前后端分离架构。

*   **前端：** 基于 Vue.js 构建单页面应用 (SPA)，负责用户界面展示和交互，通过 HTTP 请求调用后端提供的 API。
*   **后端：** 使用 Node.js 和 Express.js 构建 RESTful API 服务，负责处理前端请求、业务逻辑计算、与数据库交互，并进行数据持久化。
*   **数据库：** 使用 MongoDB 存储用户数据、任务信息、打卡记录等应用数据。

前端通过 `axios` 与后端 API 进行数据交互，并使用 Vite 的代理功能解决开发环境中的跨域问题。

## 项目结构

```
项目根目录/
├── frontend/                # 前端项目目录
│   ├── src/                 # 源代码目录
│   │   ├── components/      # 公共组件(如AppHeader)
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # Pinia状态管理
│   │   ├── styles/          # 全局样式(ancient-pixel.scss)
│   │   ├── views/           # 页面组件(Dashboard, Community, Tasks等)
│   │   ├── App.vue          # 根组件
│   │   └── main.js          # 入口文件
│   ├── index.html           # HTML模板(包含Google Fonts引用)
│   ├── vite.config.js       # Vite配置(包含代理设置)
│   └── package.json         # 依赖配置
├── backend/                 # 后端项目目录
│   ├── src/                 # 源代码目录
│   │   ├── app.js           # Express应用主文件
│   │   ├── config/          # 配置文件
│   │   ├── controllers/     # 业务逻辑控制器
│   │   ├── middlewares/     # 中间件函数
│   │   ├── models/          # Mongoose数据模型
│   │   ├── routes/          # API路由定义
│   │   └── utils/           # 工具函数
│   └── package.json         # 依赖配置
└── package.json             # 根项目依赖配置(包含workspaces设置)
```

## 已实现的页面

1. **登录/注册页面**
2. **我的工位 (Dashboard)：** 显示用户信息、打卡状态和武林秘闻
3. **修行日志 (Attendance)：** 显示考勤概览和历史打卡记录
4. **榜文任务 (Tasks)：** 任务浏览和领取
5. **江湖茶馆 (Community)：** 社区交流和帖子发布
6. **我的洞府 (Workspace)：** 个人信息和头像上传

## 样式规范 (像素武侠风格)

采用古朴的像素艺术风格，融合中国武侠元素。

*   **配色：**
    *   主色调：`--color-ancient-paper` (纸张米白), `--color-ancient-dark-brown` (深棕), `--color-ancient-ink` (墨色)。
    *   点缀色：`--color-ancient-jade` (翡翠绿), `--color-ancient-gold` (黄金色), `--color-ancient-blood-red` (血红色)。
    *   辅助色：`--color-ancient-light-brown` (浅棕), `--color-ancient-stone-gray` (石头灰), `--color-ancient-highlight` (薄荷绿)。
*   **形状：** 主要使用方正、锐利的边缘，模拟像素画的块状感。圆角极少使用或完全取消。
*   **图标：** 采用像素风格的图标，或者使用与武侠主题相关的文字符号和 emoji 作为占位符。
*   **字体：** 
    *   标题和重要文字：`ZCOOL KuaiLe` 或 `Noto Serif SC` (古风中文字体)。
    *   UI 元素和数字：`Pixelify Sans` (像素字体)。
*   **动画：** 按钮、卡片等交互元素采用像素游戏风格的"跳跃"或"按压"动画效果，伴随轻微的位移和阴影变化。
*   **响应式：** 确保页面在不同设备 (桌面/移动端) 上都能良好显示和交互。

## API 端点示例

*   `POST /api/auth/register`：用户注册
*   `POST /api/auth/login`：用户登录
*   `POST /api/clockin`：上下班打卡
*   `GET /api/clockin/summary`：获取今日打卡状态和考勤统计
*   `GET /api/tasks`：获取任务列表
*   `POST /api/tasks/:id/claim`：领取任务
*   `POST /api/tasks/:id/complete`：完成任务
*   `GET /api/community/posts`：获取社区帖子列表
*   `POST /api/community/posts`：发布新帖子
*   `GET /api/community/posts/:id`：获取帖子详情及评论
*   `POST /api/community/posts/:id/comments`：发表评论
*   `POST /api/auth/avatar`：上传用户头像

## 快速开始 (Frontend)

1.  **克隆项目**:
    ```bash
    git clone <your-repo-url>
    cd <your-project-directory>
    ```
2.  **安装依赖**:
    ```bash
    cd frontend
    npm install # 或者 yarn install
    ```
3.  **配置环境变量**:
    在 `frontend/` 目录下创建 `.env.development` 文件（如果不存在），并配置后端 API 地址：
    ```
    VITE_API_BASE_URL=http://localhost:3001
    ```
4.  **运行开发服务器**:
    ```bash
    npm run dev # 或者 yarn dev
    ```
    这将在 `http://localhost:5173` 启动前端应用。

## 快速开始 (Backend)

1.  **安装依赖**:
    ```bash
    cd backend
    npm install # 或者 yarn install
    ```
2.  **配置环境变量**:
    在 `backend/` 目录下创建 `.env` 文件，参考 `.env.example` 设置必要的环境变量。
3.  **运行开发服务器**:
    ```bash
    npm run dev # 或者 yarn dev
    ```
    这将在 `http://localhost:3001` 启动后端API服务。

## 开发注意事项

1. 前端使用 Pinia 进行状态管理
2. 使用 Vue Router 进行路由管理
3. 全局样式在 `frontend/src/styles/ancient-pixel.scss` 中定义
4. 后端 API 通过 Vite 的代理功能转发，解决跨域问题
5. 项目使用 Google Fonts 提供的中文字体，确保开发环境能够访问

---

很高兴能和你一起创造这个像素武侠江湖！
