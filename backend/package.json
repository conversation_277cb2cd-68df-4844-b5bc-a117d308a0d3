{"name": "backend", "version": "1.0.0", "description": "Express.js 后端 API 服务", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "lint": "eslint .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "api", "nodejs", "backend"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2"}, "devDependencies": {"eslint": "^8.57.0", "nodemon": "^3.1.0"}}