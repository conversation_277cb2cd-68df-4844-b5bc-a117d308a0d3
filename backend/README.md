# Express.js 后端 API 服务

这是一个基于 Express.js 框架构建的后端 API 服务。

## 项目结构

```
backend/
├── src/                  # 源代码目录
│   ├── app.js            # Express 应用主文件
│   ├── config/           # 配置文件 (数据库连接, JWT密钥等)
│   ├── controllers/      # 业务逻辑控制器
│   ├── middlewares/      # 中间件函数
│   ├── models/           # Mongoose 数据模型
│   ├── routes/           # API 路由定义
│   └── utils/            # 工具函数
├── .env                  # 环境变量 (不提交到Git)
├── .gitignore            # Git忽略配置
├── package.json          # 项目依赖配置
└── README.md             # 项目文档
```

## 技术栈

- **Node.js**: JavaScript 运行时环境
- **Express.js**: Web 应用框架
- **MongoDB/Mongoose**: 数据库和 ODM
- **JWT**: 用户认证
- **dotenv**: 环境变量管理

## 开始使用

### 安装依赖

```bash
npm install
```

### 配置环境变量

创建 `.env` 文件，参考 `.env.example` 设置必要的环境变量。

### 运行开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run start
```

## API 端点

### 基础端点

- `GET /api/health` - 健康检查
- `GET /api/message` - 示例消息接口

### 认证相关

- `POST /api/auth/signup` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh-token` - 刷新Token

## 开发指南

1. 所有新功能应在专用分支上开发
2. 路由应按功能模块分组
3. 使用控制器处理业务逻辑
4. 用中间件处理通用功能(认证、校验等)
5. 将数据库操作封装在模型文件中

## 部署

项目可部署到支持 Node.js 的任何服务器或云平台，如：
- Heroku
- Vercel
- AWS Elastic Beanstalk
- 自建服务器

## 协议

[MIT](LICENSE) 