/**
 * 工具函数集合
 * 项目中通用的辅助函数
 */

/**
 * 格式化响应数据
 * 统一 API 响应格式
 * 
 * @param {boolean} success - 操作是否成功
 * @param {string} message - 响应消息
 * @param {*} data - 响应数据
 * @param {*} error - 错误信息（仅在开发环境下返回）
 * @returns {Object} 格式化后的响应对象
 */
exports.formatResponse = (success, message, data = null, error = null) => {
  const response = {
    success,
    message,
    timestamp: new Date().toISOString()
  };

  if (data) {
    response.data = data;
  }

  if (error && process.env.NODE_ENV === 'development') {
    response.error = error;
  }

  return response;
};

/**
 * 分页辅助函数
 * 将查询参数转换为数据库查询所需的 skip 和 limit
 * 
 * @param {Object} query - 请求查询参数
 * @param {number} defaultLimit - 默认每页记录数
 * @returns {Object} 包含 skip, limit 和分页信息的对象
 */
exports.getPaginationParams = (query, defaultLimit = 10) => {
  const page = parseInt(query.page, 10) || 1;
  const limit = parseInt(query.limit, 10) || defaultLimit;
  const skip = (page - 1) * limit;

  return {
    skip,
    limit,
    page,
    pagination: {
      page,
      limit
    }
  };
};

/**
 * 构建分页元数据
 * 
 * @param {number} page - 当前页码
 * @param {number} limit - 每页记录数
 * @param {number} total - 总记录数
 * @returns {Object} 分页元数据
 */
exports.buildPaginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    currentPage: page,
    itemsPerPage: limit,
    totalItems: total,
    totalPages,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1
  };
};

/**
 * 检测对象是否为空
 * 
 * @param {Object} obj - 要检查的对象
 * @returns {boolean} 对象是否为空
 */
exports.isEmptyObject = (obj) => {
  return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
};

/**
 * 生成随机字符串
 * 
 * @param {number} length - 字符串长度
 * @returns {string} 生成的随机字符串
 */
exports.generateRandomString = (length = 10) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  return result;
}; 