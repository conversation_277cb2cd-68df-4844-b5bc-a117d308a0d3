import express from 'express';
import connectDB from './config/db.js'; // 假设 db.js 使用 ES Module 导出
import errorHandler from './middleware/errorHandler.js'; // 假设 errorHandler.js 使用 ES Module 导出
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Connect to database
// connectDB(); // 如果暂不使用数据库，可以先注释掉

// Define __dirname for ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define the upload directory path
// Assumes public is at the backend root level
const uploadDir = path.join(__dirname, '../public/uploads/avatars');

// Check if the upload directory exists, if not, create it
if (!fs.existsSync(uploadDir)) {
  console.log(`创建上传目录: ${uploadDir}`);
  fs.mkdirSync(uploadDir, { recursive: true });
}

const app = express();

// Body parser middleware to accept json
app.use(express.json());

// **临时调试路由：捕获 GET /api/community/posts**
// 如果能看到这条日志，说明请求到达了这里，问题可能在 app.use('/api/community', communityRoutes) 之后或 communityRoutes 内部。
app.get('/api/community/posts', (req, res) => {
    console.log('DEBUG: 直接在 index.js 中捕获到 GET /api/community/posts');
    res.status(200).json({ message: 'DEBUG: Request captured directly in index.js' });
});

// **直接内联社区路由定义**
// Route for creating a new post and getting posts by section
app.route('/api/community/posts') // 注意这里的完整路径
  .post(createPostController) // POST /api/community/posts
  .get(getPostsController);   // GET /api/community/posts?sectionId=:sectionId

// Route for getting a single post detail
app.route('/api/community/posts/:postId') // 注意这里的完整路径
  .get(getPostDetailController); // GET /api/community/posts/:postId

// Route for creating a comment for a specific post
app.route('/api/community/posts/:postId/comments') // 注意这里的完整路径
  .post(createCommentController); // POST /api/community/posts/:postId/comments

// Error handling middleware
app.use(errorHandler);

// Catch all other routes and return a 404 error
// app.use((req, res, next) => {
//   res.status(404).send('Route not found');
// });

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => console.log(`Server running on port ${PORT}`)); 