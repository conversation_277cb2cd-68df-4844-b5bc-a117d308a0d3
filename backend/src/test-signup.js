/**
 * 测试用户注册API
 */
require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/user.model');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 设置测试用户数据
const testUser = {
  username: 'testuser123',
  email: '<EMAIL>',
  password: 'password123'
};

// 直接测试signup逻辑
const testSignup = async () => {
  try {
    console.log('开始测试注册功能...');
    console.log(`连接到数据库: ${process.env.MONGODB_URI || 'mongodb://localhost:27017/fake_work'}`);
    
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/fake_work');
    console.log('数据库连接成功');
    
    // 检查用户是否已存在
    console.log('检查用户是否已存在...');
    const existingUser = await User.findOne({
      $or: [{ email: testUser.email }, { username: testUser.username }]
    });
    
    if (existingUser) {
      console.log('用户已存在', existingUser.username);
      // 尝试删除已存在的用户进行重新测试
      console.log('删除已存在用户，准备重新测试...');
      await User.deleteOne({ _id: existingUser._id });
    }
    
    // 创建新用户
    console.log('创建新用户...');
    const newUser = new User({
      username: testUser.username,
      email: testUser.email,
      password: testUser.password
    });
    
    // 保存用户
    console.log('保存用户到数据库...');
    await newUser.save();
    console.log('用户保存成功:', newUser._id.toString());
    
    // 生成令牌
    console.log('生成JWT令牌...');
    const token = jwt.sign(
      { id: newUser._id, role: newUser.role },
      process.env.JWT_SECRET || 'fake_work_secret_key_fallback',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
    console.log('令牌生成成功');
    
    console.log('\n===== 测试结果 =====');
    console.log('注册成功!');
    console.log('用户ID:', newUser._id.toString());
    console.log('用户名:', newUser.username);
    console.log('邮箱:', newUser.email);
    console.log('令牌:', token.substring(0, 20) + '...');
    
  } catch (error) {
    console.error('测试失败!');
    console.error('错误详情:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
    process.exit(0);
  }
};

// 执行测试
testSignup(); 