// File path: backend/src/community/community.routes.js
const express = require('express');
const { 
    createPost<PERSON>ontroller, 
    getPostsController, 
    getPostDetailController, 
    createCommentController 
} = require('./community.controller.js');
const { protect } = require('../middlewares/auth.middleware'); // Import auth middleware

const router = express.Router();

// Route for creating a new post (protected) and getting posts by section (public)
router.route('/posts')
  .post(protect, createPostController) // Protected
  .get(getPostsController);

// Route for getting a single post detail (public)
router.route('/posts/:postId')
  .get(getPostDetailController);

// Route for creating a comment for a specific post (protected)
router.route('/posts/:postId/comments')
  .post(protect, createCommentController); // Protected

module.exports = router;