// File path: backend/src/community/community.store.js
const { Post } = require('./community.model.js'); // Comment model not directly used here for top-level funcs
const mongoose = require('mongoose');

exports.getPosts = async (sectionId) => {
    console.log('Store: getPosts 函数被调用, sectionId:', sectionId);
    try {
        let query = {};
        if (sectionId) {
            query.sectionId = sectionId;
        }
        const posts = await Post.find(query)
            .populate('author', 'username avatar') // Populate post author
            // Comments author is populated by pre-hook in model now
            .sort({ timestamp: -1 })
            .lean();
        console.log('Store: Post.find 查询成功，获取到帖子数量:', posts.length);
        const postsWithCommentCount = posts.map(post => ({
            ...post,
            // Ensure author object exists before accessing username
            authorName: post.author ? post.author.username : '未知用户', 
            authorAvatar: post.author ? post.author.avatar : null,
            commentsCount: post.comments ? post.comments.length : 0
        }));
        console.log('Store: 准备返回帖子数据');
        return postsWithCommentCount;
    } catch (error) {
        console.error('Store Error: 从数据库获取帖子失败:', error);
        throw new Error('获取帖子失败');
    }
};

exports.getPostById = async (postId) => {
    try {
        const post = await Post.findById(postId)
            .populate('author', 'username avatar') // Populate post author
            // Comments author is populated by pre-hook in model now
            .lean();
        console.log('从数据库获取到帖子详情:', post ? post.title : '未找到');
        if (post && post.author) { // Add authorName and authorAvatar for consistency
            post.authorName = post.author.username;
            post.authorAvatar = post.author.avatar;
        }
        return post;
    } catch (error) {
        console.error('从数据库获取帖子详情失败:', error);
        throw new Error('获取帖子详情失败');
    }
};

// Modified to accept userId
exports.createPost = async (postData, userId) => {
    try {
        const newPost = new Post({
            ...postData,
            author: userId, // Use userId from authenticated user
        });
        await newPost.save();
        // Populate author information for the newly created post before returning
        const populatedPost = await Post.findById(newPost._id)
                                        .populate('author', 'username avatar')
                                        .lean();
        console.log('成功创建帖子:', populatedPost.title);
        if (populatedPost && populatedPost.author) {
            populatedPost.authorName = populatedPost.author.username;
            populatedPost.authorAvatar = populatedPost.author.avatar;
        }
        return populatedPost;
    } catch (error) {
        console.error('创建帖子失败:', error);
        throw new Error('创建帖子失败');
    }
};

// getCommentsForPost might not be directly used if comments are always fetched with posts
// If it is, it needs population too.
exports.getCommentsForPost = async (postId) => {
    try {
        const post = await Post.findById(postId)
            .select('comments')
            .populate('comments.author', 'username avatar') // Populate comment authors
            .lean();

        if (!post) {
            console.log(`未找到帖子 ID ${postId} 的评论`);
            return [];
        }
        console.log(`从帖子 ID ${postId} 获取到 ${post.comments ? post.comments.length : 0} 条评论`);
        // Add authorName and authorAvatar to each comment for consistency
        const formattedComments = post.comments ? post.comments.map(comment => ({
            ...comment,
            authorName: comment.author ? comment.author.username : '未知用户',
            authorAvatar: comment.author ? comment.author.avatar : null,
        })) : [];
        return formattedComments;
    } catch (error) {
        console.error(`获取帖子 ID ${postId} 的评论失败:`, error);
        throw new Error('获取评论失败');
    }
};

// Modified to accept userId
exports.createComment = async (postId, commentData, userId) => {
    try {
        const post = await Post.findById(postId);

        if (!post) {
            console.log(`创建评论失败: 未找到帖子 ID ${postId}`);
            return null;
        }

        const newComment = {
            ...commentData,
            author: userId, // Use userId from authenticated user
            timestamp: new Date(),
            _id: new mongoose.Types.ObjectId() 
        };

        post.comments.push(newComment);
        await post.save();

        // Find the newly added comment and populate its author
        // Need to fetch the post again to ensure populate works correctly on subdocuments
        const updatedPost = await Post.findById(postId)
                                      .populate('comments.author', 'username avatar')
                                      .lean();
        
        const savedComment = updatedPost.comments.find(comment => comment._id.equals(newComment._id));
        
        if (savedComment && savedComment.author) {
             savedComment.authorName = savedComment.author.username;
             savedComment.authorAvatar = savedComment.author.avatar;
        }

        console.log(`成功为帖子 ID ${postId} 添加评论:`, savedComment);
        return savedComment;
    } catch (error) {
        console.error(`为帖子 ID ${postId} 创建评论失败:`, error);
        throw new Error('创建评论失败');
    }
};