// File path: backend/src/community/community.controller.js
const communityStore = require('./community.store.js');

exports.createPostController = async (req, res, next) => {
    const { title, content, sectionId } = req.body;
    const userId = req.user.id; // Assuming auth middleware adds req.user

    if (!title || !content || !sectionId) {
        return res.status(400).json({ message: 'Title, content, and sectionId are required.' });
    }

    try {
        // Pass userId to the store function
        const newPost = await communityStore.createPost({ title, content, sectionId }, userId);
        res.status(201).json(newPost);
    } catch (error) {
        console.error('创建帖子控制器错误:', error);
        next(error); 
    }
};

exports.getPostsController = async (req, res, next) => {
    const { sectionId } = req.query; 

    try {
        const posts = await communityStore.getPosts(sectionId);
        res.status(200).json(posts);
    } catch (error) {
        console.error('获取帖子列表控制器错误:', error);
         next(error);
    }
};

exports.getPostDetailController = async (req, res, next) => {
    const { postId } = req.params; 

    try {
        const post = await communityStore.getPostById(postId);

        if (!post) {
            return res.status(404).json({ message: 'Post not found.' });
        }
        res.status(200).json(post);
    } catch (error) {
        console.error('获取帖子详情控制器错误:', error);
         next(error);
    }
};

exports.createCommentController = async (req, res, next) => {
    const { postId } = req.params; 
    const { content } = req.body;
    const userId = req.user.id; // Assuming auth middleware adds req.user

    if (!content) {
        return res.status(400).json({ message: 'Comment content is required.' });
    }

    try {
        // Pass userId to the store function
        const newComment = await communityStore.createComment(postId, { content }, userId);

        if (!newComment) {
             return res.status(404).json({ message: 'Post not found to add comment.' });
        }
        res.status(201).json(newComment);
    } catch (error) {
        console.error('创建评论控制器错误:', error);
        next(error);
    }
};