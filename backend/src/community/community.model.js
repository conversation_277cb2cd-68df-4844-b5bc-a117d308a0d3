// File path: backend/src/community/community.model.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema; // Import Schema

// 评论 Schema
const commentSchema = mongoose.Schema({
    content: { type: String, required: true },
    author: { type: Schema.Types.ObjectId, ref: 'User', required: true }, // Changed
    timestamp: { type: Date, default: Date.now },
});

// 帖子 Schema
const postSchema = mongoose.Schema({
    title: { type: String, required: true },
    content: { type: String, required: true },
    sectionId: { type: String, required: true },
    author: { type: Schema.Types.ObjectId, ref: 'User', required: true }, // Changed
    timestamp: { type: Date, default: Date.now },
    likes: { type: Number, default: 0 },
    comments: [commentSchema] 
});

// Pre-hook to populate author in comments when finding a Post
// This helps when fetching a post and its comments together
postSchema.pre(/^find/, function(next) {
    this.populate({
        path: 'comments.author',
        select: 'username avatar' // Select fields you want from User
    });
    next();
});

const Comment = mongoose.model('Comment', commentSchema);
const Post = mongoose.model('Post', postSchema);

module.exports = { Post, Comment };