// File path: backend/src/app.js
// 引入核心模块
const express = require('express');
const cors = require('cors');
const path = require('path');
const morgan = require('morgan'); // 引入 morgan 日志中间件
// 环境变量配置 (需要安装 dotenv 包)
require('dotenv').config();

// 创建 Express 应用实例
const app = express();

// 定义服务器监听端口，优先使用环境变量中的配置
// const PORT = process.env.PORT || 3001; // PORT 定义移至 server.js

// CORS 配置
const corsOptions = {
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174'], // 允许前端域名访问
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};

// 中间件配置 - 添加日志以便调试
console.log('[app.js] 配置 CORS 中间件');
app.use((req, res, next) => { console.log('[app.js] Before CORS'); next(); });
app.use(cors(corsOptions)); // 允许跨域请求，使用详细配置
app.use((req, res, next) => { console.log('[app.js] After CORS'); next(); });

console.log('[app.js] 配置 Morgan 日志中间件');
app.use((req, res, next) => { console.log('[app.js] Before Morgan'); next(); });
// 配置 morgan 日志中间件，根据环境选择日志格式
if (process.env.NODE_ENV === 'production') {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}
app.use((req, res, next) => { console.log('[app.js] After Morgan'); next(); });

console.log('[app.js] 配置 Body-parser 中间件');
// ***** 修改: 取消注释 JSON 解析中间件 *****
app.use(express.json()); 
// ***** 修改: 取消注释 URL 编码解析中间件 *****
app.use(express.urlencoded({ extended: true })); 

// 移除或简化 body-parser 周围的日志，因为它们现在已启用
// app.use((req, res, next) => { console.log('[app.js] Before express.json'); next(); });
// app.use((req, res, next) => { console.log('[app.js] After express.json'); next(); });
// app.use((req, res, next) => { console.log('[app.js] Before express.urlencoded'); next(); });
// app.use((req, res, next) => { console.log('[app.js] After express.urlencoded'); next(); });


console.log('[app.js] 配置静态文件服务');
// 如果需要提供静态文件，可以在这里配置
app.use(express.static(path.join(__dirname, '../public')));

// API 路由前缀
const API_PREFIX = process.env.API_PREFIX || '/api';
console.log(`[app.js] API 路由前缀: ${API_PREFIX}`);

// 基础路由 - 这些通常放在路由模块中，但作为快速测试可以放这里
console.log('[app.js] 配置基础路由');
app.get('/', (req, res) => {
  console.log('[app.js] 访问到 /');
  res.send('欢迎访问 API 服务器');
});

// 测试路由 - 健康检查现在简化到路由模块了
// app.get('/api/message', (req, res) => { ... });

// 引入路由模块
console.log('[app.js] 引入路由模块');
const apiRoutes = require('./routes');

// 注册 API 路由
console.log(`[app.js] 注册 API 路由到 ${API_PREFIX}`);
app.use(API_PREFIX, apiRoutes);

// 错误处理中间件
console.log('[app.js] 配置错误处理中间件');
app.use((err, req, res, next) => {
  console.error('[app.js] 遇到错误:', err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 处理 404 请求
console.log('[app.js] 配置 404 处理中间件');
app.use((req, res) => {
  console.log(`[app.js] 收到未匹配路由: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  });
});

// 导出 app 实例，方便测试
module.exports = app;