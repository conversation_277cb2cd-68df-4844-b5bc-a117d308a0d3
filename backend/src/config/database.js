// File path: backend/src/config/database.js
// ...
const mongoose = require('mongoose');
mongoose.set('debug', (collectionName, methodName, ...methodArgs) => {
  console.log(`[Mongoose Debug] ${collectionName}.${methodName}(${JSON.stringify(methodArgs)})`);
});

// 从环境变量中获取数据库连接信息
const DB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/fake_work';

// 数据库配置选项 - 移除过时选项
const dbOptions = {
  // 根据 MongoDB 版本和需求可以添加更多配置选项
};

// 连接到数据库
const connectDB = async () => {
  try {
    // ***** 调试步骤 3: 打印连接信息 *****
    console.log(`[database.js] 准备连接到 MongoDB。配置的 URI: ${DB_URI}`);
    // ***** 结束调试步骤 *****

    const conn = await mongoose.connect(DB_URI, dbOptions); // 确保 dbOptions 传递（如果需要）
    console.log(`MongoDB 连接成功: ${conn.connection.host}:${conn.connection.port}/${conn.connection.name}`);
    
    // ***** 调试步骤 4: 打印实际连接的数据库名称 *****
    console.log(`[database.js] 实际连接的主机: ${conn.connection.host}`);
    console.log(`[database.js] 实际连接的端口: ${conn.connection.port}`);
    console.log(`[database.js] 实际连接的数据库名: ${conn.connection.name}`);
    // ***** 结束调试步骤 *****
    
    return conn;
  } catch (error) {
    console.error(`MongoDB 连接失败: ${error.message}`);
    // ***** 调试步骤 5: 打印连接失败时的 URI *****
    console.error(`[database.js] 连接失败时使用的 URI: ${DB_URI}`);
    // ***** 结束调试步骤 *****
    process.exit(1);
  }
};

module.exports = {
  URI: DB_URI,
  options: dbOptions,
  connectDB
};