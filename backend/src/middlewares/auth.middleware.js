/**
 * 认证中间件
 * 用于验证用户令牌和权限
 */

const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

/**
 * 保护路由，验证JWT令牌
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.protect = async (req, res, next) => {
  try {
    // 从请求头中获取令牌
    let token;
    if (
      req.headers.authorization && 
      req.headers.authorization.startsWith('Bearer')
    ) {
      // 从 Bearer token 中提取令牌
      token = req.headers.authorization.split(' ')[1];
    }
    
    // 检查是否有令牌
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供访问令牌，请先登录'
      });
    }
    
    try {
      // 验证令牌
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // 将用户ID附加到请求对象
      req.user = {
        id: decoded.id,
        role: decoded.role
      };
      
      next();
    } catch (error) {
      // 令牌无效或已过期
      return res.status(401).json({
        success: false,
        message: '访问令牌无效或已过期'
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * 限制只有特定角色的用户才能访问
 * @param  {...string} roles - 允许访问的角色列表
 * @returns {Function} Express 中间件函数
 */
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    // 检查用户角色是否在允许的角色列表中
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '无权执行此操作'
      });
    }
    
    next();
  };
};

/**
 * 仅管理员访问
 * 确保只有具有管理员角色的用户才能访问受保护的路由
 */
exports.adminOnly = (req, res, next) => {
  // 确保先调用了 verifyToken 中间件
  if (!req.user) {
    return res.status(500).json({
      success: false,
      message: '服务器错误：未找到用户数据'
    });
  }
  
  // 检查用户角色
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足，需要管理员权限'
    });
  }
  
  next();
}; 