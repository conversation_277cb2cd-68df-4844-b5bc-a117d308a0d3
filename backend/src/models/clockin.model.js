/**
 * 打卡记录模型
 * 用于记录用户的上下班打卡时间
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * 打卡记录 Schema
 */
const clockInSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID不能为空']
    },
    date: {
      type: Date,
      default: Date.now,
      required: true
    },
    // 将日期格式化为 YYYY-MM-DD 存储，方便按天查询
    dateString: {
      type: String,
      required: true
    },
    // 打卡类型：'in' 表示上班打卡，'out' 表示下班打卡
    type: {
      type: String,
      enum: ['in', 'out'],
      required: [true, '打卡类型不能为空']
    },
    // 打卡时间，24小时格式 "HH:MM"
    time: {
      type: String,
      required: [true, '打卡时间不能为空']
    },
    // 工作时长（仅当type为'out'时有效），单位：小时
    hoursWorked: {
      type: Number,
      default: 0
    },
    // 打卡备注
    note: {
      type: String,
      default: ''
    },
    // 打卡状态：'normal'正常, 'late'迟到, 'early'早退, '补签'补卡
    status: {
      type: String,
      enum: ['normal', 'late', 'early', 'makeup'],
      default: 'normal'
    }
  },
  {
    timestamps: true
  }
);

// 创建复合索引
clockInSchema.index({ user: 1, dateString: 1, type: 1 });

const ClockIn = mongoose.model('ClockIn', clockInSchema);

module.exports = ClockIn; 