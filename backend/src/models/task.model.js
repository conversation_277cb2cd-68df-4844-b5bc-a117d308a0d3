/**
 * 任务模型
 * 用于记录系统任务和用户自定义任务
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * 任务 Schema
 */
const taskSchema = new Schema(
  {
    // 任务标题
    title: {
      type: String,
      required: [true, '任务标题不能为空'],
      trim: true,
      minlength: [2, '任务标题至少需要2个字符'],
      maxlength: [100, '任务标题不能超过100个字符']
    },
    // 任务描述
    description: {
      type: String,
      default: '',
      maxlength: [500, '任务描述不能超过500个字符']
    },
    // 任务类型：'official'(官方任务), 'personal'(个人任务)
    type: {
      type: String,
      enum: ['official', 'personal'],
      default: 'personal'
    },
    // 任务分类：'work'(工作), 'study'(学习), 'design'(设计), 'development'(开发), 'document'(文档), 'other'(其他)
    category: {
      type: String,
      enum: ['work', 'study', 'design', 'development', 'document', 'other'],
      default: 'work'
    },
    // 创建者
    creator: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '创建者不能为空']
    },
    // 执行者（可以为空，表示未分配或公开任务）
    assignee: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    // 优先级：'low'(低), 'medium'(中), 'high'(高)
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    // 截止日期
    dueDate: {
      type: Date,
      default: null
    },
    // 任务状态：'pending'(待处理), 'in_progress'(进行中), 'completed'(已完成), 'cancelled'(已取消)
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'cancelled'],
      default: 'pending'
    },
    // 完成时间
    completedAt: {
      type: Date,
      default: null
    },
    // 奖励积分
    rewardPoints: {
      type: Number,
      default: 0
    },
    // 任务标签
    tags: [{
      type: String,
      trim: true
    }],
    // 是否公开（如果是公开任务，其他用户可以领取）
    isPublic: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// 创建索引
taskSchema.index({ creator: 1 });
taskSchema.index({ assignee: 1 });
taskSchema.index({ status: 1 });
taskSchema.index({ dueDate: 1 });
taskSchema.index({ type: 1 });

const Task = mongoose.model('Task', taskSchema);

module.exports = Task; 