/**
 * 公告模型
 * 用于存储系统公告和重要通知
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * 公告 Schema
 */
const announcementSchema = new Schema(
  {
    // 公告标题
    title: {
      type: String,
      required: [true, '公告标题不能为空'],
      trim: true,
      minlength: [2, '公告标题至少需要2个字符'],
      maxlength: [100, '公告标题不能超过100个字符']
    },
    // 公告内容
    content: {
      type: String,
      required: [true, '公告内容不能为空'],
      minlength: [2, '公告内容至少需要2个字符']
    },
    // 发布者
    publisher: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '发布者不能为空']
    },
    // 公告类型：'system'(系统公告), 'activity'(活动公告), 'notice'(一般通知)
    type: {
      type: String,
      enum: ['system', 'activity', 'notice'],
      default: 'notice'
    },
    // 公告重要性：'normal'(普通), 'important'(重要), 'urgent'(紧急)
    importance: {
      type: String,
      enum: ['normal', 'important', 'urgent'],
      default: 'normal'
    },
    // 公告状态：'draft'(草稿), 'published'(已发布), 'archived'(已归档)
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'published'
    },
    // 发布日期
    publishDate: {
      type: Date,
      default: Date.now
    },
    // 过期日期（如果设置，表示公告在此日期后不再显示）
    expiryDate: {
      type: Date,
      default: null
    },
    // 阅读量
    viewCount: {
      type: Number,
      default: 0
    },
    // 已读用户列表
    readBy: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    // 是否置顶
    isPinned: {
      type: Boolean,
      default: false
    },
    // 排序优先级（值越大越靠前）
    priority: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// 创建索引
announcementSchema.index({ status: 1 });
announcementSchema.index({ publishDate: -1 });
announcementSchema.index({ isPinned: -1, publishDate: -1 });

const Announcement = mongoose.model('Announcement', announcementSchema);

module.exports = Announcement; 