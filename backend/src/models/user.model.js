// File path: backend/src/models/user.model.js
/**
 * 用户模型
 * 定义用户数据结构，用于与数据库交互
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Schema = mongoose.Schema;

/**
 * 用户 Schema
 * 定义用户数据在 MongoDB 中的结构
 */
const userSchema = new Schema(
  {
    username: {
      type: String,
      required: [true, '用户名不能为空'],
      trim: true,
      // minlength: [3, '用户名至少需要3个字符'],
      maxlength: [50, '用户名不能超过50个字符']
    },
    email: {
      type: String,
      required: [true, '邮箱不能为空'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, '请提供有效的邮箱地址']
    },
    password: {
      type: String,
      required: [true, '密码不能为空'],
      minlength: [6, '密码至少需要6个字符']
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user'
    },
    avatar: {
      type: String,
      default: '👨‍💻' // 默认使用emoji作为头像
    },
    nickname: {
      type: String,
      default: function() {
        return this.username; // 默认使用用户名作为昵称
      }
    },
    position: {
      type: String,
      default: '初级摸鱼师' // 虚拟职位
    },
    signature: { // **新增：个性签名字段**
      type: String,
      default: '此心安处是吾乡', // 默认值
      maxlength: [100, '个性签名不能超过100个字符']
    },
    stats: {
      totalHours: { type: Number, default: 0 }, // 总工时
      todayHours: { type: Number, default: 0 }, // 今日工时
      weeklyHours: { type: Number, default: 0 }, // 本周工时
      points: { type: Number, default: 100 }, // 积分
      consecutiveClockIns: { type: Number, default: 0 }, // 连续打卡天数
      totalClockIns: { type: Number, default: 0 }, // 总打卡天数
      lastDateTodayHoursUpdated: { type: String, default: '' }, // YYYY-MM-DD
      monthlyLateCount: { type: Number, default: 0 }, // **新增：本月迟到次数**
      lastMonthLateCountUpdated: { type: String, default: '' } // **新增：YYYY-MM 用于重置月度迟到计数**
    },
    preferences: {
      theme: { type: String, default: 'default' }, // 界面主题
      notifications: { type: Boolean, default: true } // 是否接收通知
    },
    isActive: {
      type: Boolean,
      default: true
    },
    lastLogin: {
      type: Date,
      default: null
    }
  },
  {
    timestamps: true // 自动添加 createdAt 和 updatedAt 字段
  }
);

// 创建索引 - 移除重复索引定义
userSchema.index({ username: 1 });

// 密码加密中间件
userSchema.pre('save', async function(next) {
  // 只有在密码被修改时才进行加密
  if (!this.isModified('password')) return next();

  try {
    // 生成盐
    const salt = await bcrypt.genSalt(10);
    // 加密密码
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 方法：比较密码
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 方法：过滤返回数据中的敏感字段
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  return user;
};

// 创建用户模型
const User = mongoose.model('User', userSchema);

module.exports = User;