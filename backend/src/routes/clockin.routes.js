/**
 * 打卡相关路由
 * 处理用户打卡、查询打卡记录等请求
 */

const express = require('express');
const router = express.Router();
const clockinController = require('../controllers/clockin.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// 所有打卡相关的路由都需要身份验证
router.use(authMiddleware.protect);

// 打卡（上班/下班）
router.post('/', clockinController.clockIn);

// 获取用户打卡记录
router.get('/', clockinController.getClockIns);

// 获取今天的打卡记录
router.get('/today', clockinController.getTodayClockIns);

// 获取用户考勤概览：今日打卡状态和统计数据
router.get('/summary', clockinController.getAttendanceSummary);

module.exports = router; 