/**
 * 任务相关路由
 * 处理任务的创建、查询、领取、完成、删除等请求
 */

const express = require('express');
const router = express.Router();
const taskController = require('../controllers/task.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// 所有任务相关的路由都需要身份验证
router.use(authMiddleware.protect);

// 创建任务（可以根据需求调整权限，例如只允许管理员或特定角色）
router.post('/', authMiddleware.restrictTo('admin', 'user'), taskController.createTask); // 暂时允许user创建，后面可再细化

// 获取所有任务列表
router.get('/', taskController.getAllTasks);

// 获取用户分配的任务列表（我领取的任务）
router.get('/my', taskController.getAllTasks); // 复用 getAllTasks，通过 query 参数 assignedToMe=true 实现

// 获取单个任务详情
router.get('/:taskId', taskController.getTaskById);

// 领取任务
router.post('/:taskId/claim', taskController.claimTask);

// 完成任务
router.post('/:taskId/complete', taskController.completeTask);

// 更新任务 (创建者或管理员)
router.put('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.updateTask); // 允许创建者更新

// 删除任务 (创建者或管理员)
router.delete('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.deleteTask); // 允许创建者删除

module.exports = router;
