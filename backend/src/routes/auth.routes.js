/**
 * 认证相关路由
 * 处理用户注册、登录、登出等认证相关请求
 */

const express = require('express');
const router = express.Router();
const { signup, login, logout, getCurrentUser, refreshToken, uploadAvatar, upload, updateProfile } = require('../controllers/auth.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// 用户注册
router.post('/signup', signup);

// 用户登录
router.post('/login', login);

// 用户登出
router.post('/logout', logout);

// 获取当前用户信息
router.get('/me', authMiddleware.protect, getCurrentUser);

// 刷新 token
router.post('/refresh-token', authMiddleware.protect, refreshToken);

// **新增** 用户头像上传路由
// 需要认证和 multer 中间件处理文件上传
router.post('/avatar', authMiddleware.protect, upload.single('avatar'), uploadAvatar);

// **新增：更新用户资料路由 (需要认证)**
router.put('/profile', authMiddleware.protect, updateProfile); // 使用 PUT 请求更新资源

module.exports = router;