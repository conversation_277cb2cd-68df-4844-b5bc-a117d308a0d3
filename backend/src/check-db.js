/**
 * MongoDB连接测试脚本
 * 用于验证数据库连接是否正常
 */

require('dotenv').config();
const mongoose = require('mongoose');

// 从环境变量中获取数据库连接信息
const DB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/fake_work';

console.log('尝试连接到数据库...');
console.log(`连接地址: ${DB_URI}`);

mongoose.connect(DB_URI)
  .then(() => {
    console.log('MongoDB连接成功!');
    console.log(`已连接到: ${mongoose.connection.host}:${mongoose.connection.port}/${mongoose.connection.name}`);
    
    // 列出所有已存在的集合
    mongoose.connection.db.listCollections().toArray()
      .then(collections => {
        console.log('当前数据库中的集合:');
        if (collections.length === 0) {
          console.log('(无集合，这是一个新的数据库)');
        } else {
          collections.forEach(collection => {
            console.log(`- ${collection.name}`);
          });
        }
        
        // 完成后关闭连接
        mongoose.connection.close();
        console.log('数据库连接已关闭');
        process.exit(0);
      })
      .catch(err => {
        console.error('列出集合时出错:', err);
        mongoose.connection.close();
        process.exit(1);
      });
  })
  .catch(err => {
    console.error('MongoDB连接失败!');
    console.error(`错误信息: ${err.message}`);
    console.error('请确保MongoDB服务已启动，且连接地址正确');
    process.exit(1);
  }); 