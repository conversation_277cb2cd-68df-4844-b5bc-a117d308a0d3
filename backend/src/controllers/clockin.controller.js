// File path: backend/src/controllers/clockin.controller.js
/**
 * 打卡控制器
 * 处理用户打卡相关的业务逻辑
 */

const ClockIn = require('../models/clockin.model');
const User = require('../models/user.model');

// **新增：标准作息时间（硬编码，未来可配置或从用户设置中读取）**
const STANDARD_WORK_START = '09:00';
const STANDARD_WORK_END = '17:00';
const GRACE_PERIOD_MINUTES = 30; // 迟到/早退的宽限时间（分钟）

/**
 * 格式化日期为YYYY-MM-DD字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDateString = (date) => {
  const d = date || new Date();
  return d.toISOString().split('T')[0];
};

/**
 * 获取当前时间的HH:MM格式
 * @returns {string} 当前时间，格式为HH:MM
 */
const getCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 将 HH:MM 时间字符串转换为分钟数
 * @param {string} timeString - HH:MM 格式的时间字符串
 * @returns {number} 从午夜开始的分钟数
 */
const formatTimeToMinutes = (timeString) => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

/**
 * 计算两个时间字符串之间的小时差
 * @param {string} startTime - 开始时间，格式为HH:MM
 * @param {string} endTime - 结束时间，格式为HH:MM
 * @returns {number} 小时差
 */
const calculateHoursWorked = (startTime, endTime) => {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);

  const startDate = new Date();
  startDate.setHours(startHours, startMinutes, 0);

  const endDate = new Date();
  endDate.setHours(endHours, endMinutes, 0);

  // 如果结束时间小于开始时间，假设是第二天
  if (endDate < startDate) {
    endDate.setDate(endDate.getDate() + 1);
  }

  const diffMs = endDate - startDate;
  const diffHours = diffMs / (1000 * 60 * 60);

  return parseFloat(diffHours.toFixed(2));
};

/**
 * 打卡（上班/下班）
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.clockIn = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { type, note } = req.body; // type: 'in' 或 'out'

    if (!type || !['in', 'out'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '打卡类型无效，必须是 "in" 或 "out"'
      });
    }

    const now = new Date();
    const currentDateString = formatDateString(now);
    const currentTime = getCurrentTime(); // 获取当前时间 HH:MM
    const currentUserTimeInMinutes = formatTimeToMinutes(currentTime);

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: '用户不存在' });
    }

    // 检查是否有相同类型的打卡记录（同一天内）
    // 对于上班卡，如果当天已打过，则提示。对于下班卡，允许多次打卡，但以最后一次为准（或业务逻辑定义）
    // 为简化，目前逻辑是：同一天内不允许重复打相同类型的卡。
    const existingRecordForTodayType = await ClockIn.findOne({
      user: userId,
      dateString: currentDateString,
      type
    });

    if (existingRecordForTodayType) {
      return res.status(400).json({
        success: false,
        message: type === 'in' ? '今天已经打过上班卡了' : '今天已经打过下班卡了'
      });
    }

    // 更新用户今日工时统计的日期标记
    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== currentDateString) {
      user.stats.todayHours = 0; // 新的一天，重置今日工时
    }
    user.stats.lastDateTodayHoursUpdated = currentDateString; // 标记今日工时已针对今天

    // **处理迟到/早退逻辑和更新用户统计**
    let clockInStatus = 'normal'; // 默认状态
    const currentMonthString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    if (type === 'in') {
      const standardStartMinutes = formatTimeToMinutes(STANDARD_WORK_START);
      if (currentUserTimeInMinutes > standardStartMinutes + GRACE_PERIOD_MINUTES) {
        clockInStatus = 'late';
        // 更新月度迟到计数
        if (user.stats.lastMonthLateCountUpdated !== currentMonthString) {
          user.stats.monthlyLateCount = 0; // 新的月份，重置月度迟到计数
          user.stats.lastMonthLateCountUpdated = currentMonthString;
        }
        user.stats.monthlyLateCount += 1;
      }
    } else if (type === 'out') {
      const standardEndMinutes = formatTimeToMinutes(STANDARD_WORK_END);
      if (currentUserTimeInMinutes < standardEndMinutes - GRACE_PERIOD_MINUTES) {
        clockInStatus = 'early';
      }

      const clockInRecordToday = await ClockIn.findOne({
        user: userId,
        dateString: currentDateString,
        type: 'in'
      }).sort({ date: -1 }); // 获取今天最新的上班卡

      if (!clockInRecordToday) {
        // 如果没有找到上班卡，下班卡逻辑不执行，但在实际中前端应该防止这种情况
        return res.status(400).json({
          success: false,
          message: '请先打今天的上班卡'
        });
      }

      const hoursWorkedForSession = calculateHoursWorked(clockInRecordToday.time, currentTime);

      const clockOutRecord = new ClockIn({
        user: userId,
        date: now,
        dateString: currentDateString,
        type,
        time: currentTime, // 使用当前时间
        hoursWorked: hoursWorkedForSession, // 记录本次会话工时
        note,
        status: clockInStatus // 应用计算出的状态
      });
      await clockOutRecord.save();

      user.stats.todayHours += hoursWorkedForSession;
      user.stats.weeklyHours += hoursWorkedForSession; // 简单累加，复杂周计算后续再考虑
      user.stats.totalHours += hoursWorkedForSession;
      await user.save(); // 保存用户统计更新

      return res.status(201).json({
        success: true,
        message: '下班打卡成功',
        clockIn: clockOutRecord,
        hoursWorked: user.stats.todayHours, // 返回当天的总工时
        monthlyLateCount: user.stats.monthlyLateCount // 返回本月迟到计数
      });
    }

    // type === 'in' (上班打卡)
    await user.save(); // 保存可能的 todayHours 重置和月度迟到统计更新

    const newClockInRecord = new ClockIn({
      user: userId,
      date: now,
      dateString: currentDateString,
      type,
      time: currentTime, // 使用当前时间
      note,
      status: clockInStatus // 应用计算出的状态
    });

    await newClockInRecord.save();

    res.status(201).json({
      success: true,
      message: '上班打卡成功',
      clockIn: newClockInRecord,
      monthlyLateCount: user.stats.monthlyLateCount // 返回本月迟到计数
    });

  } catch (error) {
    next(error);
  }
};

// ... getClockIns, getTodayClockIns remains the same ...
exports.getClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, type, year, month } = req.query; // **修改：新增 year 和 month 筛选**

    // 构建查询条件
    const query = { user: userId };

    // 如果提供了年和月，则优先按月查询
    if (year && month) {
        // 构建该年月的开始和结束日期字符串
        const startOfMonth = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, 1));
        const endOfMonth = new Date(Date.UTC(parseInt(year), parseInt(month), 0, 23, 59, 59, 999)); // 月份参数为0表示上个月的最后一天

        const startOfMonthString = formatDateString(startOfMonth);
        const endOfMonthString = formatDateString(endOfMonth);

        query.dateString = { $gte: startOfMonthString, $lte: endOfMonthString };
    }
    // 如果提供了开始日期和结束日期，按日期范围查询 (兼容旧的，优先级低于年/月)
    else if (startDate && endDate) {
      query.dateString = { $gte: startDate, $lte: endDate };
    } else if (startDate) {
      query.dateString = { $gte: startDate };
    } else if (endDate) {
      query.dateString = { $lte: endDate };
    }

    // 如果提供了打卡类型，按类型查询
    if (type && ['in', 'out'].includes(type)) {
      query.type = type;
    }

    const clockIns = await ClockIn.find(query)
      .sort({ date: -1 }) // 按日期倒序
      .limit(parseInt(req.query.limit) || 100); // 增加默认限制，或移除限制以获取全月数据

    res.json({
      success: true,
      clockIns
    });
  } catch (error) {
    next(error);
  }
};

exports.getTodayClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const today = formatDateString(new Date());

    const clockIns = await ClockIn.find({
      user: userId,
      dateString: today
    }).sort({ date: 1 });

    res.json({
      success: true,
      clockIns,
      today
    });
  } catch (error) {
    next(error);
  }
};


/**
 * 获取用户考勤概览：今日打卡状态和统计数据
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getAttendanceSummary = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const todayDateString = formatDateString(new Date());
    const currentMonthString = `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

    const user = await User.findById(userId).select('stats');
    if (!user) {
        return res.status(404).json({ success: false, message: '用户不存在' });
    }

    let statsForFrontend = { ...user.stats }; // Make a copy to modify for response

    // 1. 处理 todayHours 和打卡状态的逻辑
    let todayStatus = {
      clockedIn: false,
      clockInTime: '',
      clockOutTime: '',
    };

    // 如果上次更新的日期不是今天，重置今日工时
    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== todayDateString) {
      statsForFrontend.todayHours = 0;
    }
    // 如果月度迟到计数不是当前月份的，重置月度迟到计数
    if (user.stats.lastMonthLateCountUpdated !== currentMonthString) {
      statsForFrontend.monthlyLateCount = 0;
    }


    // 获取今日的打卡记录来判断当前状态
    const todayRecords = await ClockIn.find({
      user: userId,
      dateString: todayDateString
    }).sort({ date: 1 }); // 升序获取，in在前，out在后

    let firstIn = null;
    let lastOut = null;

    todayRecords.forEach(record => {
        if (record.type === 'in') {
            // 找到当天最早的上班卡
            if (!firstIn || record.date < firstIn.date) firstIn = record;
        }
        if (record.type === 'out') {
            // 找到当天最晚的下班卡
            if (!lastOut || record.date > lastOut.date) lastOut = record;
        }
    });

    if (firstIn) {
        todayStatus.clockedIn = true;
        todayStatus.clockInTime = firstIn.time;
    }
    if (lastOut) {
        todayStatus.clockOutTime = lastOut.time;
    }

    // 2. 模拟/准备其他统计数据 (如请假等)
    statsForFrontend.leaveCount = statsForFrontend.leaveCount || 0; // Placeholder

    res.json({
      success: true,
      message: '获取考勤概览成功',
      data: {
        todayStatus,
        stats: { // 明确要返回的统计数据
          todayHours: statsForFrontend.todayHours,
          monthlyLateCount: statsForFrontend.monthlyLateCount, // **返回月度迟到计数**
          leaveCount: statsForFrontend.leaveCount,
          totalHours: statsForFrontend.totalHours, // 返回总工时
          weeklyHours: statsForFrontend.weeklyHours, // 返回周工时
        }
      }
    });

  } catch (error) {
    next(error);
  }
};