/**
 * 任务控制器
 * 处理任务的创建、查询、领取、完成等业务逻辑
 */

const Task = require('../models/task.model');
const User = require('../models/user.model');

/**
 * 创建新任务（通常由管理员或系统调用）
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.createTask = async (req, res, next) => {
  try {
    const { title, description, type, category, priority, dueDate, rewardPoints, tags, isPublic } = req.body;
    const creator = req.user.id; // 任务创建者（从认证中间件获取）

    // 调试日志：打印接收到的数据
    console.log('接收到的任务创建数据:', {
      title, description, type, category, priority, dueDate, rewardPoints, tags, isPublic
    });
    console.log('rewardPoints 类型:', typeof rewardPoints, '值:', rewardPoints);

    if (!title || !description || !rewardPoints) {
      return res.status(400).json({ success: false, message: '标题、描述和奖励积分是必填项。' });
    }

    // 确保 rewardPoints 是数字类型
    const validatedRewardPoints = parseInt(rewardPoints, 10);
    if (isNaN(validatedRewardPoints) || validatedRewardPoints < 0) {
      return res.status(400).json({ success: false, message: '奖励积分必须是有效的正整数。' });
    }

    const newTask = new Task({
      title,
      description,
      type: type || 'personal', // 默认个人任务
      category: category || 'work', // 默认工作分类
      creator,
      priority: priority || 'medium',
      dueDate,
      rewardPoints: validatedRewardPoints, // 使用验证后的值
      tags: tags || [],
      isPublic: isPublic || false,
      status: 'pending' // 默认待处理
    });

    const savedTask = await newTask.save();

    // 调试日志：打印保存后的数据
    console.log('保存后的任务数据:', {
      title: savedTask.title,
      rewardPoints: savedTask.rewardPoints,
      rewardPointsType: typeof savedTask.rewardPoints
    });

    res.status(201).json({ success: true, message: '任务创建成功', task: savedTask });
  } catch (error) {
    console.error('创建任务失败:', error);
    next(error);
  }
};

/**
 * 获取所有任务列表
 * 支持筛选：type, status, assignee, isPublic (如果用户是管理员，可以查看所有)
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getAllTasks = async (req, res, next) => {
  try {
    const { type, status, category, isPublic, assignedToMe } = req.query;
    const userId = req.user.id; // 当前登录用户ID

    let query = {};

    // 默认只显示公开任务，除非明确要求或管理员权限
    if (req.user.role !== 'admin') {
      query.isPublic = true;
    } else if (isPublic !== undefined) { // 如果管理员明确设置isPublic
        query.isPublic = isPublic === 'true';
    }


    if (type) query.type = type;
    if (status) query.status = status;
    if (category) query.category = category;

    // 如果查询"我领取的任务"
    if (assignedToMe === 'true' && userId) {
      query.assignee = userId;
    }

    // 排除已取消的任务（除非明确查询cancelled）
    if (query.status !== 'cancelled') {
        query.status = { $ne: 'cancelled' };
    }

    const tasks = await Task.find(query)
      .populate('creator', 'username nickname avatar') // 填充创建者信息
      .populate('assignee', 'username nickname avatar') // 填充执行者信息
      .sort({ createdAt: -1 }); // 最新创建的在前

    res.json({ success: true, tasks });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    next(error);
  }
};

/**
 * 获取单个任务详情
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getTaskById = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId)
      .populate('creator', 'username nickname avatar')
      .populate('assignee', 'username nickname avatar');

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    // 检查任务是否公开，或者当前用户是创建者或执行者或管理员
    if (!task.isPublic && task.creator.toString() !== userId && task.assignee?.toString() !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: '无权查看此任务。' });
    }

    res.json({ success: true, task });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    next(error);
  }
};

/**
 * 领取任务
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.claimTask = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // 当前登录用户ID

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    if (!task.isPublic) {
        return res.status(403).json({ success: false, message: '此任务不可公开领取。' });
    }

    if (task.status !== 'pending') {
      return res.status(400).json({ success: false, message: '该任务已被领取或已完成。' });
    }

    // 领取任务
    task.assignee = userId;
    task.status = 'in_progress';
    await task.save();

    res.json({ success: true, message: '榜文领取成功，开始您的江湖历练吧！', task });
  } catch (error) {
    console.error('领取任务失败:', error);
    next(error);
  }
};

/**
 * 完成任务
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.completeTask = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // 当前登录用户ID

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    // 只有任务的执行者或管理员才能完成任务
    if (task.assignee?.toString() !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: '无权完成此任务。' });
    }

    if (task.status === 'completed') {
      return res.status(400).json({ success: false, message: '该任务已完成。' });
    }
    if (task.status !== 'in_progress') {
        return res.status(400).json({ success: false, message: '该任务尚未开始或处于无效状态。' });
    }

    // 完成任务
    task.status = 'completed';
    task.completedAt = new Date();
    await task.save();

    // 奖励积分给执行者
    const user = await User.findById(userId);
    if (user) {
      user.stats.points += task.rewardPoints;
      await user.save();
    }

    res.json({ success: true, message: `恭喜您完成任务，获得 ${task.rewardPoints} 内力值！`, task });
  } catch (error) {
    console.error('完成任务失败:', error);
    next(error);
  }
};

// **新增：删除任务（管理员/创建者）**
exports.deleteTask = async (req, res, next) => {
    try {
        const { taskId } = req.params;
        const userId = req.user.id;

        const task = await Task.findById(taskId);
        if (!task) {
            return res.status(404).json({ success: false, message: '任务不存在。' });
        }

        // 只有创建者或管理员才能删除任务
        if (task.creator.toString() !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ success: false, message: '无权删除此任务。' });
        }

        await task.deleteOne(); // 使用 deleteOne 而不是 remove
        res.json({ success: true, message: '任务已成功删除。' });
    } catch (error) {
        console.error('删除任务失败:', error);
        next(error);
    }
};

// **新增：更新任务（管理员/创建者）**
exports.updateTask = async (req, res, next) => {
    try {
        const { taskId } = req.params;
        const userId = req.user.id;
        const updates = req.body;

        const task = await Task.findById(taskId);
        if (!task) {
            return res.status(404).json({ success: false, message: '任务不存在。' });
        }

        // 只有创建者或管理员才能更新任务
        if (task.creator.toString() !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ success: false, message: '无权更新此任务。' });
        }

        // 允许更新的字段
        const allowedUpdates = ['title', 'description', 'type', 'category', 'priority', 'dueDate', 'rewardPoints', 'tags', 'isPublic', 'status', 'assignee'];
        Object.keys(updates).forEach(key => {
            if (allowedUpdates.includes(key)) {
                if (key === 'assignee' && updates[key] === '') { // 允许取消分配
                    task[key] = null;
                } else if (key === 'tags' && !Array.isArray(updates[key])) { // 确保tags是数组
                    task[key] = updates[key].split(',').map(tag => tag.trim());
                }
                else {
                    task[key] = updates[key];
                }
            }
        });

        // 如果手动修改状态为 completed，自动设置 completedAt
        if (updates.status === 'completed' && !task.completedAt) {
            task.completedAt = new Date();
        } else if (updates.status !== 'completed' && task.completedAt) {
            // 如果状态不再是 completed，清空 completedAt
            task.completedAt = null;
        }

        await task.save();
        res.json({ success: true, message: '任务更新成功。', task });
    } catch (error) {
        console.error('更新任务失败:', error);
        next(error);
    }
};
