// File path: backend/src/server.js
/**
 * 服务器入口文件
 */
require('dotenv').config();
const app = require('./app'); // ***** 修改: 加载 Express app *****
// const http = require('http'); // 移除或注释掉：不再需要原始 http 模块创建服务器
const { connectDB } = require('./config/database');

// 定义服务器监听端口，优先使用环境变量中的配置
const PORT = process.env.PORT || 3001;

// ***** 移除或注释掉：不再创建原始 HTTP 服务器 *****
// const server = http.createServer((req, res) => {
//   console.log(`[Raw HTTP Server] 收到请求: ${req.method} ${req.url}`);
//   res.writeHead(200, { 'Content-Type': 'text/plain' });
//   res.end('Hello from raw Node.js server!');
// });


// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    await connectDB();

    // ***** 修改: 启动 Express 应用服务器 *****
    app.listen(PORT, () => { 
      console.log(`[Express Server] 服务器正在监听端口: ${PORT}`);
      console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`API 地址: http://localhost:${PORT}${process.env.API_PREFIX || '/api'}`);
    });
  } catch (error) {
    console.error('[Express Server] 服务器启动失败:', error.message);
    process.exit(1);
  }
};

// 启动服务器
startServer();

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  // 应用不会立即崩溃，但可以进行日志记录或其他操作
});