# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@eslint-community/eslint-utils@^4.2.0":
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz#de633db3ec2ef6a3c89e2f19038063e8a122e2c2"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz#fb907624df3256d04b9aa2df50d7aa97ec648748"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@mapbox/node-pre-gyp@^1.0.11":
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/@mapbox/node-pre-gyp/download/@mapbox/node-pre-gyp-1.0.11.tgz#417db42b7f5323d79e93b34a6d7a2a12c0df43fa"
  integrity sha1-QX20K39TI9eek7NKbXoqEsDfQ/o=
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@mongodb-js/saslprep@^1.1.9":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@mongodb-js/saslprep/download/@mongodb-js/saslprep-1.2.2.tgz#09506f29cc2a99d9d7b951caa7fffc87e522a6d3"
  integrity sha1-CVBvKcwqmdnXuVHKp//8h+UiptM=
  dependencies:
    sparse-bitfield "^3.0.3"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/@types/webidl-conversions/download/@types/webidl-conversions-7.0.3.tgz#1306dbfa53768bcbcfc95a1c8cde367975581859"
  integrity sha1-Ewbb+lN2i8vPyVocjN42eXVYGFk=

"@types/whatwg-url@^11.0.2":
  version "11.0.5"
  resolved "http://r.npm.sankuai.com/@types/whatwg-url/download/@types/whatwg-url-11.0.5.tgz#aaa2546e60f0c99209ca13360c32c78caf2c409f"
  integrity sha1-qqJUbmDwyZIJyhM2DDLHjK8sQJ8=
  dependencies:
    "@types/webidl-conversions" "*"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

abbrev@1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/accepts/download/accepts-2.0.0.tgz#bbcf4ba5075467f3f2131eab3cffc73c2f5d7895"
  integrity sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=
  dependencies:
    mime-types "^3.0.0"
    negotiator "^1.0.0"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.9.0:
  version "8.14.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz#721d5dc10f7d5b5609a891773d47731796935dfb"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

agent-base@6:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz#49fff58577cfee3f37176feab4c22e00f86d7f77"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-field@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/append-field/download/append-field-1.0.0.tgz#1e3440e915f0b1203d23748e78edd7b9b5b43e56"
  integrity sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/aproba/download/aproba-2.0.0.tgz#52520b8ae5b569215b354efc0caa3fe1e45a8adc"
  integrity sha1-UlILiuW1aSFbNU78DKo/4eRaitw=

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/are-we-there-yet/download/are-we-there-yet-2.0.0.tgz#372e0e7bd279d8e94c653aaa1f67200884bf3e1c"
  integrity sha1-Ny4Oe9J52OlMZTqqH2cgCIS/Phw=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

basic-auth@~2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/basic-auth/download/basic-auth-2.0.1.tgz#b998279bf47ce38344b4f3cf916d4679bbf51e3a"
  integrity sha1-uZgnm/R844NEtPPPkW1Gebv1Hjo=
  dependencies:
    safe-buffer "5.1.2"

bcrypt@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/bcrypt/download/bcrypt-5.1.1.tgz#0f732c6dcb4e12e5b70a25e326a72965879ba6e2"
  integrity sha1-D3MsbctOEuW3CiXjJqcpZYebpuI=
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.11"
    node-addon-api "^5.0.0"

bcryptjs@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/bcryptjs/download/bcryptjs-3.0.2.tgz#caadcca1afefe372ed6e20f86db8e8546361c1ca"
  integrity sha1-yq3Moa/v43LtbiD4bbjoVGNhwco=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

body-parser@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/body-parser/download/body-parser-2.2.0.tgz#f7a9656de305249a715b549b7b8fd1ab9dfddcfa"
  integrity sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=
  dependencies:
    bytes "^3.1.2"
    content-type "^1.0.5"
    debug "^4.4.0"
    http-errors "^2.0.0"
    iconv-lite "^0.6.3"
    on-finished "^2.4.1"
    qs "^6.14.0"
    raw-body "^3.0.0"
    type-is "^2.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

bson@^6.10.3:
  version "6.10.3"
  resolved "http://r.npm.sankuai.com/bson/download/bson-6.10.3.tgz#5f9a463af6b83e264bedd08b236d1356a30eda47"
  integrity sha1-X5pGOva4PiZL7dCLI20TVqMO2kc=

buffer-equal-constant-time@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz#f8e71132f7ffe6e01a5c9697a4c6f3e48d5cc819"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

busboy@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/busboy/download/busboy-1.6.0.tgz#966ea36a9502e43cdb9146962523b92f531f6893"
  integrity sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=
  dependencies:
    streamsearch "^1.1.0"

bytes@3.1.2, bytes@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bound@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

chalk@^4.0.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.2:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz#15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece"
  integrity sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-support@^1.1.2:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-support/download/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"
  integrity sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

content-disposition@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/content-disposition/download/content-disposition-1.0.0.tgz#844426cb398f934caefcbb172200126bc7ceace2"
  integrity sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

cookie-signature@^1.2.1:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.2.2.tgz#57c7fc3cc293acab9fec54d73e15690ebe4a1793"
  integrity sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=

cookie@^0.7.1:
  version "0.7.2"
  resolved "http://r.npm.sankuai.com/cookie/download/cookie-0.7.2.tgz#556369c472a2ba910f2979891b526b3436237ed7"
  integrity sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cors@^2.8.5:
  version "2.8.5"
  resolved "http://r.npm.sankuai.com/cors/download/cors-2.8.5.tgz#eac11da51592dd86b9f06f6e7ac293b3df875d29"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

cross-spawn@^7.0.2:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

date-fns@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/date-fns/download/date-fns-4.1.0.tgz#64b3d83fff5aa80438f5b1a633c2e83b8a1c2d14"
  integrity sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=

debug@2.6.9:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4, debug@4.x, debug@^4, debug@^4.3.1, debug@^4.3.2, debug@^4.3.5, debug@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.0.tgz#2b3f2aea2ffeb776477460267377dc8710faba8a"
  integrity sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=
  dependencies:
    ms "^2.1.3"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

detect-libc@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/detect-libc/download/detect-libc-2.0.4.tgz#f04715b8ba815e53b4d8109655b6508a6865a7e8"
  integrity sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dotenv@^16.5.0:
  version "16.5.0"
  resolved "http://r.npm.sankuai.com/dotenv/download/dotenv-16.5.0.tgz#092b49f25f808f020050051d1ff258e404c78692"
  integrity sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz#ae0f0fa2d85045ef14a817daa3ce9acd0489e5bf"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-2.0.0.tgz#7b8ea898077d7e409d3ac45474ea38eaf0857a58"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^8.57.0:
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz#7df109654aba7e3bbe5c8eae533c5e461d3c6ca9"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@^1.8.1:
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

express@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/express/download/express-5.1.0.tgz#d31beaf715a0016f0d53f47d3b4d7acf28c75cc9"
  integrity sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=
  dependencies:
    accepts "^2.0.0"
    body-parser "^2.2.0"
    content-disposition "^1.0.0"
    content-type "^1.0.5"
    cookie "^0.7.1"
    cookie-signature "^1.2.1"
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    finalhandler "^2.1.0"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    merge-descriptors "^2.0.0"
    mime-types "^3.0.0"
    on-finished "^2.4.1"
    once "^1.4.0"
    parseurl "^1.3.3"
    proxy-addr "^2.0.7"
    qs "^6.14.0"
    range-parser "^1.2.1"
    router "^2.2.0"
    send "^1.1.0"
    serve-static "^2.2.0"
    statuses "^2.0.1"
    type-is "^2.0.1"
    vary "^1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/finalhandler/download/finalhandler-2.1.0.tgz#72306373aa89d05a8242ed569ed86a1bff7c561f"
  integrity sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=
  dependencies:
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    on-finished "^2.4.1"
    parseurl "^1.3.3"
    statuses "^2.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/fresh/download/fresh-2.0.0.tgz#8dd7df6a1b3a1b3a5cf186c05a5dd267622635a4"
  integrity sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz#7f5036fdbf12c63c169190cbe4199c852271f9fb"
  integrity sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

gauge@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/gauge/download/gauge-3.0.2.tgz#03bf4441c044383908bcfa0656ad91803259b395"
  integrity sha1-A79EQcBEODkIvPoGVq2RgDJZs5U=
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

get-intrinsic@^1.2.5, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.19.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

http-errors@2.0.0, http-errors@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

iconv-lite@0.6.3, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz#48ca6d72f6c6a3af00a9ad4ae6876be3889e2b09"
  integrity sha1-SMptcvbGo68Aqa1K5odr44ieKwk=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-promise@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/is-promise/download/is-promise-4.0.0.tgz#42ff9f84206c1991d26debf520dd5c01042dd2f3"
  integrity sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

jsonwebtoken@^9.0.2:
  version "9.0.2"
  resolved "http://r.npm.sankuai.com/jsonwebtoken/download/jsonwebtoken-9.0.2.tgz#65ff91f4abef1784697d40952bb1998c504caaf3"
  integrity sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

jwa@^1.4.1:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/jwa/download/jwa-1.4.2.tgz#16011ac6db48de7b102777e57897901520eec7b9"
  integrity sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=
  dependencies:
    buffer-equal-constant-time "^1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/jws/download/jws-3.2.2.tgz#001099f3639468c9414000e99995fa52fb478304"
  integrity sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

kareem@2.6.3:
  version "2.6.3"
  resolved "http://r.npm.sankuai.com/kareem/download/kareem-2.6.3.tgz#23168ec8ffb6c1abfd31b7169a6fb1dd285992ac"
  integrity sha1-IxaOyP+2wav9MbcWmm+x3ShZkqw=

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/lodash.includes/download/lodash.includes-4.3.0.tgz#60bb98a87cb923c68ca1e51325483314849f553f"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz#6c2e171db2a257cd96802fd43b01b20d5f5870f6"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/lodash.isinteger/download/lodash.isinteger-4.0.4.tgz#619c0af3d03f8b04c31f5882840b77b11cd68343"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz#3ce76810c5928d03352301ac287317f11c0b1ffc"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/lodash.isstring/download/lodash.isstring-4.0.1.tgz#d527dfb5456eca7cc9bb95d5daeaf88ba54a5451"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.once/download/lodash.once-4.1.1.tgz#0dd3971213c7c56df880977d504c88fb471a97ac"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

make-dir@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

media-typer@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/media-typer/download/media-typer-1.1.0.tgz#6ab74b8f2d3320f2064b2a87a38e7931ff3a5561"
  integrity sha1-ardLjy0zIPIGSyqHo455Mf86VWE=

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/memory-pager/download/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha1-2HUWVdItOEaCdByXLyw9bfo+ZrU=

merge-descriptors@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-2.0.0.tgz#ea922f660635a2249ee565e0449f951e6b603808"
  integrity sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-db@^1.54.0:
  version "1.54.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz#cddb3ee4f9c64530dff640236661d42cb6a314f5"
  integrity sha1-zds+5PnGRTDf9kAjZmHULLajFPU=

mime-types@^3.0.0, mime-types@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-3.0.1.tgz#b1d94d6997a9b32fd69ebaed0db73de8acb519ce"
  integrity sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=
  dependencies:
    mime-db "^1.54.0"

mime-types@~2.1.24:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.6:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

minipass@^3.0.0:
  version "3.3.6"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz#7bba384db3a1520d18c9c0e5251c3444e95dd94a"
  integrity sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz#3e9788ffb90b694a5d0ec94479a45b5d8738133d"
  integrity sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=

minizlib@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz#e90d3466ba209b932451508a11ce3d3632145931"
  integrity sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^0.5.4:
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mongodb-connection-string-url@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/mongodb-connection-string-url/download/mongodb-connection-string-url-3.0.2.tgz#e223089dfa0a5fa9bf505f8aedcbc67b077b33e7"
  integrity sha1-4iMInfoKX6m/UF+K7cvGewd7M+c=
  dependencies:
    "@types/whatwg-url" "^11.0.2"
    whatwg-url "^14.1.0 || ^13.0.0"

mongodb@~6.16.0:
  version "6.16.0"
  resolved "http://r.npm.sankuai.com/mongodb/download/mongodb-6.16.0.tgz#2a7a1986ec151d9c738fc8ce4cf4324c3f728a2f"
  integrity sha1-KnoZhuwVHZxzj8jOTPQyTD9yii8=
  dependencies:
    "@mongodb-js/saslprep" "^1.1.9"
    bson "^6.10.3"
    mongodb-connection-string-url "^3.0.0"

mongoose@^8.14.2:
  version "8.14.2"
  resolved "http://r.npm.sankuai.com/mongoose/download/mongoose-8.14.2.tgz#5f36170ba16f31dba775b5dca070969c59d4bb3f"
  integrity sha1-XzYXC6FvMdundbXcoHCWnFnUuz8=
  dependencies:
    bson "^6.10.3"
    kareem "2.6.3"
    mongodb "~6.16.0"
    mpath "0.9.0"
    mquery "5.0.0"
    ms "2.1.3"
    sift "17.1.3"

morgan@^1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/morgan/download/morgan-1.10.0.tgz#091778abc1fc47cd3509824653dae1faab6b17d7"
  integrity sha1-CRd4q8H8R801CYJGU9rh+qtrF9c=
  dependencies:
    basic-auth "~2.0.1"
    debug "2.6.9"
    depd "~2.0.0"
    on-finished "~2.3.0"
    on-headers "~1.0.2"

mpath@0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/mpath/download/mpath-0.9.0.tgz#0c122fe107846e31fc58c75b09c35514b3871904"
  integrity sha1-DBIv4QeEbjH8WMdbCcNVFLOHGQQ=

mquery@5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/mquery/download/mquery-5.0.0.tgz#a95be5dfc610b23862df34a47d3e5d60e110695d"
  integrity sha1-qVvl38YQsjhi3zSkfT5dYOEQaV0=
  dependencies:
    debug "4.x"

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.3, ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multer@^1.4.5-lts.2:
  version "1.4.5-lts.2"
  resolved "http://r.npm.sankuai.com/multer/download/multer-1.4.5-lts.2.tgz#340af065d8685dda846ec9e3d7655fcd50afba2d"
  integrity sha1-NArwZdhoXdqEbsnj12VfzVCvui0=
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-1.0.0.tgz#b6c91bb47172d69f93cfd7c357bbb529019b5f6a"
  integrity sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=

node-addon-api@^5.0.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-5.1.0.tgz#49da1ca055e109a23d537e9de43c09cca21eb762"
  integrity sha1-SdocoFXhCaI9U36d5DwJzKIet2I=

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

nodemon@^3.1.0:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/nodemon/download/nodemon-3.1.10.tgz#5015c5eb4fffcb24d98cf9454df14f4fecec9bc1"
  integrity sha1-UBXF60//yyTZjPlFTfFPT+zsm8E=
  dependencies:
    chokidar "^3.5.2"
    debug "^4"
    ignore-by-default "^1.0.1"
    minimatch "^3.1.2"
    pstree.remy "^1.1.8"
    semver "^7.5.3"
    simple-update-notifier "^2.0.0"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.5"

nopt@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/nopt/download/nopt-5.0.0.tgz#530942bb58a512fccafe53fe210f13a25355dc88"
  integrity sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=
  dependencies:
    abbrev "1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npmlog@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/npmlog/download/npmlog-5.0.1.tgz#f06678e80e29419ad67ab964e0fa69959c1eb8b0"
  integrity sha1-8GZ46A4pQZrWerlk4PpplZweuLA=
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

object-assign@^4, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz#8375265e21bc20d0fa582c22e1b13485d6e00213"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

on-finished@^2.4.1:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parseurl@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-to-regexp@^8.0.0:
  version "8.2.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-8.2.0.tgz#73990cc29e57a3ff2a0d914095156df5db79e8b4"
  integrity sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

proxy-addr@^2.0.7:
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

pstree.remy@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/pstree.remy/download/pstree.remy-1.1.8.tgz#c242224f4a67c21f686839bbdb4ac282b8373d3a"
  integrity sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=

punycode@^2.1.0, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

qs@^6.14.0:
  version "6.14.0"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz#c63fa40680d2c5c941412a0e899c89af60c0a930"
  integrity sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=
  dependencies:
    side-channel "^1.1.0"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

range-parser@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/raw-body/download/raw-body-3.0.0.tgz#25b3476f07a51600619dae3fe82ddc28a36e5e0f"
  integrity sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.6.3"
    unpipe "1.0.0"

readable-stream@^2.2.2:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

router@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/router/download/router-2.2.0.tgz#019be620b711c87641167cc79b99090f00b146ef"
  integrity sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=
  dependencies:
    debug "^4.4.0"
    depd "^2.0.0"
    is-promise "^4.0.0"
    parseurl "^1.3.3"
    path-to-regexp "^8.0.0"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

semver@^6.0.0:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.5, semver@^7.5.3, semver@^7.5.4:
  version "7.7.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.1.tgz#abd5098d82b18c6c81f6074ff2647fd3e7220c9f"
  integrity sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=

send@^1.1.0, send@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/send/download/send-1.2.0.tgz#32a7554fb777b831dfa828370f773a3808d37212"
  integrity sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=
  dependencies:
    debug "^4.3.5"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    mime-types "^3.0.1"
    ms "^2.1.3"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    statuses "^2.0.1"

serve-static@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/serve-static/download/serve-static-2.2.0.tgz#9c02564ee259bdd2251b82d659a2e7e1938d66f9"
  integrity sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=
  dependencies:
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    parseurl "^1.3.3"
    send "^1.2.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

sift@17.1.3:
  version "17.1.3"
  resolved "http://r.npm.sankuai.com/sift/download/sift-17.1.3.tgz#9d2000d4d41586880b0079b5183d839c7a142bf7"
  integrity sha1-nSAA1NQVhogLAHm1GD2DnHoUK/c=

signal-exit@^3.0.0:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

simple-update-notifier@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/simple-update-notifier/download/simple-update-notifier-2.0.0.tgz#d70b92bdab7d6d90dfd73931195a30b6e3d7cebb"
  integrity sha1-1wuSvat9bZDf1zkxGVowtuPXzrs=
  dependencies:
    semver "^7.5.3"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/sparse-bitfield/download/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha1-/0rm5oZWBWuks+eSqzM004JzyhE=
  dependencies:
    memory-pager "^1.0.2"

statuses@2.0.1, statuses@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/streamsearch/download/streamsearch-1.1.0.tgz#404dd1e2247ca94af554e841a8ef0eaa238da764"
  integrity sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

supports-color@^5.5.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

tar@^6.1.11:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/tar/download/tar-6.2.1.tgz#717549c541bc3c2af15751bea94b1dd068d4b03a"
  integrity sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

touch@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/touch/download/touch-3.1.1.tgz#097a23d7b161476435e5c1344a95c0f75b4a5694"
  integrity sha1-CXoj17FhR2Q15cE0SpXA91tKVpQ=

tr46@^5.1.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-5.1.1.tgz#96ae867cddb8fdb64a49cc3059a8d428bcf238ca"
  integrity sha1-lq6GfN24/bZKScwwWajUKLzyOMo=
  dependencies:
    punycode "^2.3.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-is@^1.6.4:
  version "1.6.18"
  resolved "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type-is@^2.0.0, type-is@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/type-is/download/type-is-2.0.1.tgz#64f6cf03f92fce4015c2b224793f6bdd4b068c97"
  integrity sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=
  dependencies:
    content-type "^1.0.5"
    media-typer "^1.1.0"
    mime-types "^3.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

undefsafe@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/undefsafe/download/undefsafe-2.0.5.tgz#38733b9327bdcd226db889fb723a6efd162e6e2c"
  integrity sha1-OHM7kye9zSJtuIn7cjpu/RYubiw=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

vary@^1, vary@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

"whatwg-url@^14.1.0 || ^13.0.0":
  version "14.2.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-14.2.0.tgz#4ee02d5d725155dae004f6ae95c73e7ef5d95663"
  integrity sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/wide-align/download/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

xtend@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
