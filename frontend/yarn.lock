# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz"
  integrity sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.27.2.tgz"
  integrity sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=

"@babel/core@^7.24.5":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.27.1.tgz"
  integrity sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helpers" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.1.tgz"
  integrity sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=
  dependencies:
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.1.tgz"
  integrity sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-compilation-targets@^7.27.1":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz"
  integrity sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    semver "^6.3.1"

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz"
  integrity sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.1.tgz"
  integrity sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz"
  integrity sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz"
  integrity sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz"
  integrity sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.27.1.tgz"
  integrity sha1-/8JwEwOGB826MojmksNhHAahiqQ=
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/parser@8.0.0-alpha.12":
  version "8.0.0-alpha.12"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-8.0.0-alpha.12.tgz"
  integrity sha1-pysV2ozyAeXpmEq2rhpbY/Mq9rw=

"@babel/parser@^7.25.3", "@babel/parser@^7.27.1", "@babel/parser@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.2.tgz"
  integrity sha1-V3UYvtsXos5CEq/QUuAfffCUESc=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/plugin-syntax-jsx@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-typescript@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.27.1.tgz"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz"
  integrity sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typescript@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.27.1.tgz"
  integrity sha1-07tlWYvs4D93MRHojMTo5QcPEUA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"

"@babel/preset-typescript@^7.24.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/preset-typescript/download/@babel/preset-typescript-7.27.1.tgz"
  integrity sha1-GQdCpkKNKCMGZIpVsFKbVhSE+RI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-typescript" "^7.27.1"

"@babel/template@^7.27.1":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.1.tgz"
  integrity sha1-TbdykCsTO73dHE96fuR3YcG58pE=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.1.tgz"
  integrity sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@esbuild/aix-ppc64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.4.tgz#830d6476cbbca0c005136af07303646b419f1162"
  integrity sha1-gw1kdsu8oMAFE2rwcwNka0GfEWI=

"@esbuild/android-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.4.tgz#d11d4fc299224e729e2190cacadbcc00e7a9fd67"
  integrity sha1-0R1PwpkiTnKeIZDKytvMAOep/Wc=

"@esbuild/android-arm@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.4.tgz#5660bd25080553dd2a28438f2a401a29959bd9b1"
  integrity sha1-VmC9JQgFU90qKEOPKkAaKZWb2bE=

"@esbuild/android-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.4.tgz#18ddde705bf984e8cd9efec54e199ac18bc7bee1"
  integrity sha1-GN3ecFv5hOjNnv7FThmawYvHvuE=

"@esbuild/darwin-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.4.tgz"
  integrity sha1-sLf7VduPxvXeWgIHrphuucR2bmc=

"@esbuild/darwin-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.4.tgz#e6813fdeba0bba356cb350a4b80543fbe66bf26f"
  integrity sha1-5oE/3roLujVss1CkuAVD++Zr8m8=

"@esbuild/freebsd-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.4.tgz#dc11a73d3ccdc308567b908b43c6698e850759be"
  integrity sha1-3BGnPTzNwwhWe5CLQ8ZpjoUHWb4=

"@esbuild/freebsd-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.4.tgz#91da08db8bd1bff5f31924c57a81dab26e93a143"
  integrity sha1-kdoI24vRv/XzGSTFeoHasm6ToUM=

"@esbuild/linux-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.4.tgz#efc15e45c945a082708f9a9f73bfa8d4db49728a"
  integrity sha1-78FeRclFoIJwj5qfc7+o1NtJcoo=

"@esbuild/linux-arm@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.4.tgz#9b93c3e54ac49a2ede6f906e705d5d906f6db9e8"
  integrity sha1-m5PD5UrEmi7eb5BucF1dkG9tueg=

"@esbuild/linux-ia32@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.4.tgz#be8ef2c3e1d99fca2d25c416b297d00360623596"
  integrity sha1-vo7yw+HZn8otJcQWspfQA2BiNZY=

"@esbuild/linux-loong64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.4.tgz#b0840a2707c3fc02eec288d3f9defa3827cd7a87"
  integrity sha1-sIQKJwfD/ALuwojT+d76OCfNeoc=

"@esbuild/linux-mips64el@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.4.tgz#2a198e5a458c9f0e75881a4e63d26ba0cf9df39f"
  integrity sha1-KhmOWkWMnw51iBpOY9JroM+d858=

"@esbuild/linux-ppc64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.4.tgz#64f4ae0b923d7dd72fb860b9b22edb42007cf8f5"
  integrity sha1-ZPSuC5I9fdcvuGC5si7bQgB8+PU=

"@esbuild/linux-riscv64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.4.tgz#fb2844b11fdddd39e29d291c7cf80f99b0d5158d"
  integrity sha1-+yhEsR/d3TninSkcfPgPmbDVFY0=

"@esbuild/linux-s390x@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.4.tgz#1466876e0aa3560c7673e63fdebc8278707bc750"
  integrity sha1-FGaHbgqjVgx2c+Y/3ryCeHB7x1A=

"@esbuild/linux-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.4.tgz#c10fde899455db7cba5f11b3bccfa0e41bf4d0cd"
  integrity sha1-wQ/eiZRV23y6XxGzvM+g5Bv00M0=

"@esbuild/netbsd-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.4.tgz#02e483fbcbe3f18f0b02612a941b77be76c111a4"
  integrity sha1-AuSD+8vj8Y8LAmEqlBt3vnbBEaQ=

"@esbuild/netbsd-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.4.tgz#ec401fb0b1ed0ac01d978564c5fc8634ed1dc2ed"
  integrity sha1-7EAfsLHtCsAdl4VkxfyGNO0dwu0=

"@esbuild/openbsd-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.4.tgz#f272c2f41cfea1d91b93d487a51b5c5ca7a8c8c4"
  integrity sha1-8nLC9Bz+odkbk9SHpRtcXKeoyMQ=

"@esbuild/openbsd-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.4.tgz#2e25950bc10fa9db1e5c868e3d50c44f7c150fd7"
  integrity sha1-LiWVC8EPqdseXIaOPVDET3wVD9c=

"@esbuild/sunos-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.4.tgz#cd596fa65a67b3b7adc5ecd52d9f5733832e1abd"
  integrity sha1-zVlvplpns7etxezVLZ9XM4MuGr0=

"@esbuild/win32-arm64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.4.tgz#b4dbcb57b21eeaf8331e424c3999b89d8951dc88"
  integrity sha1-tNvLV7Ie6vgzHkJMOZm4nYlR3Ig=

"@esbuild/win32-ia32@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.4.tgz#410842e5d66d4ece1757634e297a87635eb82f7a"
  integrity sha1-QQhC5dZtTs4XV2NOKXqHY164L3o=

"@esbuild/win32-x64@0.25.4":
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.4.tgz#0b17ec8a70b2385827d52314c1253160a0b9bacc"
  integrity sha1-CxfsinCyOFgn1SMUwSUxYKC5usw=

"@floating-ui/core@^1.7.0":
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/@floating-ui/core/download/@floating-ui/core-1.7.0.tgz"
  integrity sha1-Gv8nqZPqGyVKWGMYwpw7FuoPTQo=
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0", "@floating-ui/dom@^1.6.13":
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/@floating-ui/dom/download/@floating-ui/dom-1.7.0.tgz"
  integrity sha1-+fg+5P7nisI62eZbEo/BGieFdTI=
  dependencies:
    "@floating-ui/core" "^1.7.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "http://r.npm.sankuai.com/@floating-ui/utils/download/@floating-ui/utils-0.2.9.tgz"
  integrity sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=

"@floating-ui/vue@^1.1.6":
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/@floating-ui/vue/download/@floating-ui/vue-1.1.6.tgz"
  integrity sha1-HH6PJX+uW3GnLRDBdG5rC6M4OZw=
  dependencies:
    "@floating-ui/dom" "^1.0.0"
    "@floating-ui/utils" "^0.2.9"
    vue-demi ">=0.13.0"

"@internationalized/date@^3.5.0":
  version "3.8.0"
  resolved "http://r.npm.sankuai.com/@internationalized/date/download/@internationalized/date-3.8.0.tgz"
  integrity sha1-JPswECkiQ1E4Gqh8uoU8oQk68JQ=
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/number@^3.5.0":
  version "3.6.1"
  resolved "http://r.npm.sankuai.com/@internationalized/number/download/@internationalized/number-3.6.1.tgz"
  integrity sha1-fBPMVetUaqPUK41eesfbaaCC/sc=
  dependencies:
    "@swc/helpers" "^0.5.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@parcel/watcher-android-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.1.tgz#507f836d7e2042f798c7d07ad19c3546f9848ac1"
  integrity sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=

"@parcel/watcher-darwin-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.1.tgz"
  integrity sha1-PSbc443mWQ73nEfsLFV5PAatT2c=

"@parcel/watcher-darwin-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.1.tgz#99f3af3869069ccf774e4ddfccf7e64fd2311ef8"
  integrity sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=

"@parcel/watcher-freebsd-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.1.tgz#14d6857741a9f51dfe51d5b08b7c8afdbc73ad9b"
  integrity sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=

"@parcel/watcher-linux-arm-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.1.tgz#43c3246d6892381db473bb4f663229ad20b609a1"
  integrity sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=

"@parcel/watcher-linux-arm-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.1.tgz#663750f7090bb6278d2210de643eb8a3f780d08e"
  integrity sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=

"@parcel/watcher-linux-arm64-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.1.tgz#ba60e1f56977f7e47cd7e31ad65d15fdcbd07e30"
  integrity sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=

"@parcel/watcher-linux-arm64-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.1.tgz#f7fbcdff2f04c526f96eac01f97419a6a99855d2"
  integrity sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=

"@parcel/watcher-linux-x64-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.1.tgz#4d2ea0f633eb1917d83d483392ce6181b6a92e4e"
  integrity sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=

"@parcel/watcher-linux-x64-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.1.tgz#277b346b05db54f55657301dd77bdf99d63606ee"
  integrity sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=

"@parcel/watcher-win32-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.1.tgz#7e9e02a26784d47503de1d10e8eab6cceb524243"
  integrity sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=

"@parcel/watcher-win32-ia32@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.1.tgz#2d0f94fa59a873cdc584bf7f6b1dc628ddf976e6"
  integrity sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=

"@parcel/watcher-win32-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.1.tgz#ae52693259664ba6f2228fa61d7ee44b64ea0947"
  integrity sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=

"@parcel/watcher@^2.4.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher/download/@parcel/watcher-2.5.1.tgz"
  integrity sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.1"
    "@parcel/watcher-darwin-arm64" "2.5.1"
    "@parcel/watcher-darwin-x64" "2.5.1"
    "@parcel/watcher-freebsd-x64" "2.5.1"
    "@parcel/watcher-linux-arm-glibc" "2.5.1"
    "@parcel/watcher-linux-arm-musl" "2.5.1"
    "@parcel/watcher-linux-arm64-glibc" "2.5.1"
    "@parcel/watcher-linux-arm64-musl" "2.5.1"
    "@parcel/watcher-linux-x64-glibc" "2.5.1"
    "@parcel/watcher-linux-x64-musl" "2.5.1"
    "@parcel/watcher-win32-arm64" "2.5.1"
    "@parcel/watcher-win32-ia32" "2.5.1"
    "@parcel/watcher-win32-x64" "2.5.1"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@rollup/rollup-android-arm-eabi@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.40.2.tgz#c228d00a41f0dbd6fb8b7ea819bbfbf1c1157a10"
  integrity sha1-wijQCkHw29b7i36oGbv78cEVehA=

"@rollup/rollup-android-arm64@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.40.2.tgz#e2b38d0c912169fd55d7e38d723aada208d37256"
  integrity sha1-4rONDJEhaf1V1+ONcjqtogjTclY=

"@rollup/rollup-darwin-arm64@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.40.2.tgz"
  integrity sha1-H92zaQ8q4z3xbTNMYTN38Fq+SHg=

"@rollup/rollup-darwin-x64@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.40.2.tgz#818298d11c8109e1112590165142f14be24b396d"
  integrity sha1-gYKY0RyBCeERJZAWUULxS+JLOW0=

"@rollup/rollup-freebsd-arm64@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.40.2.tgz#91a28dc527d5bed7f9ecf0e054297b3012e19618"
  integrity sha1-kaKNxSfVvtf57PDgVCl7MBLhlhg=

"@rollup/rollup-freebsd-x64@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.40.2.tgz#28acadefa76b5c7bede1576e065b51d335c62c62"
  integrity sha1-KKyt76drXHvt4VduBltR0zXGLGI=

"@rollup/rollup-linux-arm-gnueabihf@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.40.2.tgz#819691464179cbcd9a9f9d3dc7617954840c6186"
  integrity sha1-gZaRRkF5y82an509x2F5VIQMYYY=

"@rollup/rollup-linux-arm-musleabihf@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.40.2.tgz#d149207039e4189e267e8724050388effc80d704"
  integrity sha1-0UkgcDnkGJ4mfockBQOI7/yA1wQ=

"@rollup/rollup-linux-arm64-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.40.2.tgz#fa72ebddb729c3c6d88973242f1a2153c83e86ec"
  integrity sha1-+nLr3bcpw8bYiXMkLxohU8g+huw=

"@rollup/rollup-linux-arm64-musl@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.40.2.tgz#2054216e34469ab8765588ebf343d531fc3c9228"
  integrity sha1-IFQhbjRGmrh2VYjr80PVMfw8kig=

"@rollup/rollup-linux-loongarch64-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.40.2.tgz#818de242291841afbfc483a84f11e9c7a11959bc"
  integrity sha1-gY3iQikYQa+/xIOoTxHpx6EZWbw=

"@rollup/rollup-linux-powerpc64le-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.40.2.tgz#0bb4cb8fc4a2c635f68c1208c924b2145eb647cb"
  integrity sha1-C7TLj8SixjX2jBIIySSyFF62R8s=

"@rollup/rollup-linux-riscv64-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.40.2.tgz#4b3b8e541b7b13e447ae07774217d98c06f6926d"
  integrity sha1-SzuOVBt7E+RHrgd3QhfZjAb2km0=

"@rollup/rollup-linux-riscv64-musl@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.40.2.tgz#e065405e67d8bd64a7d0126c931bd9f03910817f"
  integrity sha1-4GVAXmfYvWSn0BJskxvZ8DkQgX8=

"@rollup/rollup-linux-s390x-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.40.2.tgz#dda3265bbbfe16a5d0089168fd07f5ebb2a866fe"
  integrity sha1-3aMmW7v+FqXQCJFo/Qf167KoZv4=

"@rollup/rollup-linux-x64-gnu@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.40.2.tgz#90993269b8b995b4067b7b9d72ff1c360ef90a17"
  integrity sha1-kJkyabi5lbQGe3udcv8cNg75Chc=

"@rollup/rollup-linux-x64-musl@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.40.2.tgz#fdf5b09fd121eb8d977ebb0fda142c7c0167b8de"
  integrity sha1-/fWwn9Eh642XfrsP2hQsfAFnuN4=

"@rollup/rollup-win32-arm64-msvc@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.40.2.tgz#6397e1e012db64dfecfed0774cb9fcf89503d716"
  integrity sha1-Y5fh4BLbZN/s/tB3TLn8+JUD1xY=

"@rollup/rollup-win32-ia32-msvc@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.40.2.tgz#df0991464a52a35506103fe18d29913bf8798a0c"
  integrity sha1-3wmRRkpSo1UGED/hjSmRO/h5igw=

"@rollup/rollup-win32-x64-msvc@4.40.2":
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.40.2.tgz#8dae04d01a2cbd84d6297d99356674c6b993f0fc"
  integrity sha1-ja4E0BosvYTWKX2ZNWZ0xrmT8Pw=

"@swc/helpers@^0.5.0":
  version "0.5.17"
  resolved "http://r.npm.sankuai.com/@swc/helpers/download/@swc/helpers-0.5.17.tgz"
  integrity sha1-WnvpWsDwvxhufm6JDnpvbNps6XE=
  dependencies:
    tslib "^2.8.0"

"@tanstack/virtual-core@3.13.8":
  version "3.13.8"
  resolved "http://r.npm.sankuai.com/@tanstack/virtual-core/download/@tanstack/virtual-core-3.13.8.tgz"
  integrity sha1-Y0bmiFIcHwhvUIzL66rQtHKirvs=

"@tanstack/vue-virtual@^3.12.0":
  version "3.13.8"
  resolved "http://r.npm.sankuai.com/@tanstack/vue-virtual/download/@tanstack/vue-virtual-3.13.8.tgz"
  integrity sha1-XfIUsljm9izndaK/qNiRkEOiu2A=
  dependencies:
    "@tanstack/virtual-core" "3.13.8"

"@ts-morph/common@~0.25.0":
  version "0.25.0"
  resolved "http://r.npm.sankuai.com/@ts-morph/common/download/@ts-morph/common-0.25.0.tgz"
  integrity sha1-t2y9UXEYrMjq2vErL8LUf0KSNFI=
  dependencies:
    minimatch "^9.0.4"
    path-browserify "^1.0.1"
    tinyglobby "^0.2.9"

"@types/estree@1.0.7":
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.7.tgz"
  integrity sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=

"@types/web-bluetooth@^0.0.21":
  version "0.0.21"
  resolved "http://r.npm.sankuai.com/@types/web-bluetooth/download/@types/web-bluetooth-0.0.21.tgz"
  integrity sha1-UlQzx4Su2bRXqqDuPZKutx80a2M=

"@unovue/detypes@^0.8.5":
  version "0.8.5"
  resolved "http://r.npm.sankuai.com/@unovue/detypes/download/@unovue/detypes-0.8.5.tgz"
  integrity sha1-fmIDGa0hpnnf2xj8t2RLcQQdaEM=
  dependencies:
    "@babel/core" "^7.24.5"
    "@babel/preset-typescript" "^7.24.1"
    "@vue/compiler-dom" "^3.4.27"
    "@vue/compiler-sfc" "^3.4.27"
    "@vuedx/template-ast-types" "0.7.1"
    fast-glob "^3.3.2"
    prettier "^3.2.5"
    typescript "^5.4.5"

"@vitejs/plugin-vue@^5.2.3":
  version "5.2.3"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-vue/download/@vitejs/plugin-vue-5.2.3.tgz"
  integrity sha1-caj8gtTS5CWvMEw1vziVBvZ02Js=

"@vue/compiler-core@3.5.13", "@vue/compiler-core@^3.0.0":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/compiler-core/download/@vue/compiler-core-3.5.13.tgz"
  integrity sha1-sK5sQ0f2DAPoSaBdNOW/dHyb2gU=
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/shared" "3.5.13"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@3.5.13", "@vue/compiler-dom@^3.4.27":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.13.tgz"
  integrity sha1-uxuHWNvFQrNljdqXO5ihyTEailg=
  dependencies:
    "@vue/compiler-core" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/compiler-sfc@3.5.13", "@vue/compiler-sfc@^3.4.27", "@vue/compiler-sfc@^3.5":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.13.tgz"
  integrity sha1-Rh+L00O1wG+sQYnE/vivMt6oK0Y=
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/compiler-core" "3.5.13"
    "@vue/compiler-dom" "3.5.13"
    "@vue/compiler-ssr" "3.5.13"
    "@vue/shared" "3.5.13"
    estree-walker "^2.0.2"
    magic-string "^0.30.11"
    postcss "^8.4.48"
    source-map-js "^1.2.0"

"@vue/compiler-ssr@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.13.tgz"
  integrity sha1-53GtzKbT0AD5GkJ3yXKpltB/Q7o=
  dependencies:
    "@vue/compiler-dom" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/devtools-api@^6.6.4":
  version "6.6.4"
  resolved "http://r.npm.sankuai.com/@vue/devtools-api/download/@vue/devtools-api-6.6.4.tgz"
  integrity sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=

"@vue/devtools-api@^7.7.2":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-api/download/@vue/devtools-api-7.7.6.tgz#4af5dbc77bcc8543f0a8e6f029f598ed978d6c7d"
  integrity sha1-SvXbx3vMhUPwqObwKfWY7ZeNbH0=
  dependencies:
    "@vue/devtools-kit" "^7.7.6"

"@vue/devtools-kit@^7.7.6":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-kit/download/@vue/devtools-kit-7.7.6.tgz#3d9cbe2378a65ed7c4baa77ecc0f7ecdfd185fbb"
  integrity sha1-PZy+I3imXtfEuqd+zA9+zf0YX7s=
  dependencies:
    "@vue/devtools-shared" "^7.7.6"
    birpc "^2.3.0"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.2"

"@vue/devtools-shared@^7.7.6":
  version "7.7.6"
  resolved "http://r.npm.sankuai.com/@vue/devtools-shared/download/@vue/devtools-shared-7.7.6.tgz#5da2218df61b605b7b88e725241fc6640df0e4b5"
  integrity sha1-XaIhjfYbYFt7iOclJB/GZA3w5LU=
  dependencies:
    rfdc "^1.4.1"

"@vue/reactivity@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/reactivity/download/@vue/reactivity-3.5.13.tgz"
  integrity sha1-tB/yu4ZeCTiZoiIZ9bJfl7b+FV8=
  dependencies:
    "@vue/shared" "3.5.13"

"@vue/runtime-core@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/runtime-core/download/@vue/runtime-core-3.5.13.tgz"
  integrity sha1-H6+kvwuXrw692dv+mM1jDaNjpFU=
  dependencies:
    "@vue/reactivity" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/runtime-dom@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/runtime-dom/download/@vue/runtime-dom-3.5.13.tgz"
  integrity sha1-YQ/Hld6SRjAOiuiGWTDVNOEkYhU=
  dependencies:
    "@vue/reactivity" "3.5.13"
    "@vue/runtime-core" "3.5.13"
    "@vue/shared" "3.5.13"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/server-renderer/download/@vue/server-renderer-3.5.13.tgz"
  integrity sha1-Qp6tYu5R3niWRsIu/pCOSJqtRvc=
  dependencies:
    "@vue/compiler-ssr" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/shared@3.5.13":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@vue/shared/download/@vue/shared-3.5.13.tgz"
  integrity sha1-h7MJpjecIrkm5paJMjeCb2Qzm28=

"@vuedx/template-ast-types@0.7.1":
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/@vuedx/template-ast-types/download/@vuedx/template-ast-types-0.7.1.tgz"
  integrity sha1-zMdXhqT+HRkQ9sjZPRUNRO4dq9w=
  dependencies:
    "@vue/compiler-core" "^3.0.0"

"@vueuse/core@^12.5.0":
  version "12.8.2"
  resolved "http://r.npm.sankuai.com/@vueuse/core/download/@vueuse/core-12.8.2.tgz"
  integrity sha1-AHxt0pp9H2kz6RbnovjvPD+Wjqo=
  dependencies:
    "@types/web-bluetooth" "^0.0.21"
    "@vueuse/metadata" "12.8.2"
    "@vueuse/shared" "12.8.2"
    vue "^3.5.13"

"@vueuse/metadata@12.8.2":
  version "12.8.2"
  resolved "http://r.npm.sankuai.com/@vueuse/metadata/download/@vueuse/metadata-12.8.2.tgz"
  integrity sha1-bLOk6Xzc9SgynuvBvac81/ZDGNM=

"@vueuse/shared@12.8.2", "@vueuse/shared@^12.5.0":
  version "12.8.2"
  resolved "http://r.npm.sankuai.com/@vueuse/shared/download/@vueuse/shared-12.8.2.tgz"
  integrity sha1-ueRhHQYDYpyOFR+YJFnaOU4i+TA=
  dependencies:
    vue "^3.5.13"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.14.0, acorn@^8.9.0:
  version "8.14.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

ajv@^8.0.1:
  version "8.17.1"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-8.17.1.tgz"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/arg/download/arg-5.0.2.tgz"
  integrity sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-hidden@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/aria-hidden/download/aria-hidden-1.2.4.tgz"
  integrity sha1-t444P9vATQV2LHi0olpQHnNsRSI=
  dependencies:
    tslib "^2.0.0"

ast-types@^0.14.2:
  version "0.14.2"
  resolved "http://r.npm.sankuai.com/ast-types/download/ast-types-0.14.2.tgz"
  integrity sha1-YAuILfhYPjzU8t9fog+oN1nUvf0=
  dependencies:
    tslib "^2.0.1"

ast-types@^0.16.1:
  version "0.16.1"
  resolved "http://r.npm.sankuai.com/ast-types/download/ast-types-0.16.1.tgz"
  integrity sha1-ep2hYXyQgbwSH6r+kXEbTIu4HaI=
  dependencies:
    tslib "^2.0.1"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

axios@^1.9.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz"
  integrity sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

birpc@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/birpc/download/birpc-2.3.0.tgz#e5a402dc785ef952a2383ef3cfc075e0842f3e8c"
  integrity sha1-5aQC3Hhe+VKiOD7zz8B14IQvPow=

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0:
  version "4.24.5"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.5.tgz"
  integrity sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/camelcase-css/download/camelcase-css-2.0.1.tgz"
  integrity sha1-7pePaUeRTMMMa0R0G27R338EP9U=

caniuse-lite@^1.0.30001716:
  version "1.0.30001717"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001717.tgz"
  integrity sha1-XZ/sXOCXlqGJMBOCVRBniSisoSk=

chalk@^5.3.0:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-5.4.1.tgz"
  integrity sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=

chokidar@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-4.0.3.tgz"
  integrity sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=
  dependencies:
    readdirp "^4.0.1"

citty@^0.1.6:
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/citty/download/citty-0.1.6.tgz"
  integrity sha1-D3kE2h7UYl4anqfg+ngJgaq3xeQ=
  dependencies:
    consola "^3.2.3"

cli-cursor@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-5.0.0.tgz"
  integrity sha1-JKSDHs9aawHd6zL7caSyCIsNzjg=
  dependencies:
    restore-cursor "^5.0.0"

cli-progress@^3.12.0:
  version "3.12.0"
  resolved "http://r.npm.sankuai.com/cli-progress/download/cli-progress-3.12.0.tgz"
  integrity sha1-gH7hS2a8wIYljkRK0PGefUJXeUI=
  dependencies:
    string-width "^4.2.3"

cli-spinners@^2.9.2:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

code-block-writer@^13.0.3:
  version "13.0.3"
  resolved "http://r.npm.sankuai.com/code-block-writer/download/code-block-writer-13.0.3.tgz"
  integrity sha1-kPioR2OlAS2nr2ExndY4ZVrpC1s=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^12.1.0:
  version "12.1.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-12.1.0.tgz"
  integrity sha1-AUI7NvUBJZ/arE0OTWDJbJkVhdM=

commander@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-4.1.1.tgz"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

confbox@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/confbox/download/confbox-0.1.8.tgz"
  integrity sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=

consola@^3.2.3, consola@^3.4.0:
  version "3.4.2"
  resolved "http://r.npm.sankuai.com/consola/download/consola-3.4.2.tgz"
  integrity sha1-WvEQFFOXu2ev2rdwE/3DTK5ZDqc=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

copy-anything@^3.0.2:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/copy-anything/download/copy-anything-3.0.5.tgz#2d92dce8c498f790fa7ad16b01a1ae5a45b020a0"
  integrity sha1-LZLc6MSY95D6etFrAaGuWkWwIKA=
  dependencies:
    is-what "^4.1.8"

cosmiconfig@^9.0.0:
  version "9.0.0"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-9.0.0.tgz"
  integrity sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=
  dependencies:
    env-paths "^2.2.1"
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"

cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-select@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-5.1.0.tgz"
  integrity sha1-uOvWVUw2N8zHZoiAStP2pv2uqKY=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-what@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

css@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/css/download/css-3.0.0.tgz"
  integrity sha1-REek1Y/dAzZ8UWyp9krjZc7kql0=
  dependencies:
    inherits "^2.0.4"
    source-map "^0.6.1"
    source-map-resolve "^0.6.0"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.0.tgz"
  integrity sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=
  dependencies:
    ms "^2.1.3"

decode-uri-component@^0.2.0:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz"
  integrity sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=

deep-diff@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/deep-diff/download/deep-diff-1.0.2.tgz"
  integrity sha1-r9PR90kRW+ll6Jxj7cersVBrnCY=

deepmerge@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

defu@^6.1.4:
  version "6.1.4"
  resolved "http://r.npm.sankuai.com/defu/download/defu-6.1.4.tgz"
  integrity sha1-Tgyc+f9o/l89fydlzBoBLf3LBHk=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

destr@^2.0.3:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/destr/download/destr-2.0.5.tgz"
  integrity sha1-fREv8bkl+40gefrFvbSpCXO1H9s=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/detect-libc/download/detect-libc-1.0.3.tgz"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/didyoumean/download/didyoumean-1.2.2.tgz"
  integrity sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=

diff@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/diff/download/diff-7.0.0.tgz"
  integrity sha1-P7NNOHzXbYA/buvqZ7kh2rAYKpo=

dlv@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/dlv/download/dlv-1.1.3.tgz"
  integrity sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-2.0.0.tgz"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-5.0.3.tgz"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-3.2.2.tgz"
  integrity sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

electron-to-chromium@^1.5.149:
  version "1.5.151"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.151.tgz"
  integrity sha1-Xt1sF+Gy8UtGYsQbk3n5bMjCu3w=

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-10.4.0.tgz"
  integrity sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

entities@^4.2.0, entities@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

env-paths@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/env-paths/download/env-paths-2.2.1.tgz"
  integrity sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

esbuild@^0.25.0:
  version "0.25.4"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.25.4.tgz"
  integrity sha1-u5oWM01O8sM8cwGpJLi4YzUaCFQ=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.4"
    "@esbuild/android-arm" "0.25.4"
    "@esbuild/android-arm64" "0.25.4"
    "@esbuild/android-x64" "0.25.4"
    "@esbuild/darwin-arm64" "0.25.4"
    "@esbuild/darwin-x64" "0.25.4"
    "@esbuild/freebsd-arm64" "0.25.4"
    "@esbuild/freebsd-x64" "0.25.4"
    "@esbuild/linux-arm" "0.25.4"
    "@esbuild/linux-arm64" "0.25.4"
    "@esbuild/linux-ia32" "0.25.4"
    "@esbuild/linux-loong64" "0.25.4"
    "@esbuild/linux-mips64el" "0.25.4"
    "@esbuild/linux-ppc64" "0.25.4"
    "@esbuild/linux-riscv64" "0.25.4"
    "@esbuild/linux-s390x" "0.25.4"
    "@esbuild/linux-x64" "0.25.4"
    "@esbuild/netbsd-arm64" "0.25.4"
    "@esbuild/netbsd-x64" "0.25.4"
    "@esbuild/openbsd-arm64" "0.25.4"
    "@esbuild/openbsd-x64" "0.25.4"
    "@esbuild/sunos-x64" "0.25.4"
    "@esbuild/win32-arm64" "0.25.4"
    "@esbuild/win32-ia32" "0.25.4"
    "@esbuild/win32-x64" "0.25.4"

escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

eslint-scope@^7.1.1:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

espree@^9.3.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@~4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.2.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.3.2:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-uri@^3.0.1:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/fast-uri/download/fast-uri-3.0.6.tgz"
  integrity sha1-iPEwt3z66iN41Wv5cN6iElemh0g=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fdir@^6.4.4:
  version "6.4.4"
  resolved "http://r.npm.sankuai.com/fdir/download/fdir-6.4.4.tgz"
  integrity sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz"
  integrity sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.2.tgz"
  integrity sha1-Ncq73TDDznPessQtPI0+2cpReUw=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.12"

fs-extra@^11.2.0, fs-extra@^11.3.0:
  version "11.3.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-11.3.0.tgz"
  integrity sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-east-asian-width@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-east-asian-width/download/get-east-asian-width-1.3.0.tgz"
  integrity sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=

get-intrinsic@^1.2.6:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-own-enumerable-keys@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/get-own-enumerable-keys/download/get-own-enumerable-keys-1.0.0.tgz"
  integrity sha1-WbvaD350acjHQIbgj3nxOBsgOJk=

get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-tsconfig@^4.10.0:
  version "4.10.0"
  resolved "http://r.npm.sankuai.com/get-tsconfig/download/get-tsconfig-4.10.0.tgz"
  integrity sha1-QDpoKzc6gjYSR1pMKSjHMm/A9rs=
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  resolved "http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^11.0.0:
  version "11.0.2"
  resolved "http://r.npm.sankuai.com/glob/download/glob-11.0.2.tgz"
  integrity sha1-MmHjiXu8YDAwsEH9d7pjYCLVHOA=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^4.0.1"
    minimatch "^10.0.0"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^2.0.0"

glob@^7.1.6:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

gonzales-pe@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/gonzales-pe/download/gonzales-pe-4.3.0.tgz"
  integrity sha1-/p3sXzxVfurQn/hoxlgmvlTQZ7M=
  dependencies:
    minimist "^1.2.5"

gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

he@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hookable@^5.5.3:
  version "5.5.3"
  resolved "http://r.npm.sankuai.com/hookable/download/hookable-5.5.3.tgz#6cfc358984a1ef991e2518cb9ed4a778bbd3215d"
  integrity sha1-bPw1iYSh75keJRjLntSneLvTIV0=

immutable@^5.0.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-5.1.2.tgz"
  integrity sha1-6BaUdkFFBeWk+mUBB7ZeEifRbUs=

import-fresh@^3.3.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-2.0.0.tgz"
  integrity sha1-QMV2FFk4JtoRAK3mBZd41ZfxbpA=

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-obj/download/is-obj-3.0.0.tgz"
  integrity sha1-sIifH5+MuH6H31Oo0SMKIlD4ub4=

is-regexp@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/is-regexp/download/is-regexp-3.1.0.tgz"
  integrity sha1-AjXquc2luD+WrEomPYwyydWtdCI=

is-unicode-supported@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-1.3.0.tgz"
  integrity sha1-2CSYS2FsKSouGYIH1KYJmDhC9xQ=

is-unicode-supported@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-2.1.0.tgz"
  integrity sha1-CfCrDebTdE1I0mXruY9l0R8qmzo=

is-what@^4.1.8:
  version "4.1.16"
  resolved "http://r.npm.sankuai.com/is-what/download/is-what-4.1.16.tgz#1ad860a19da8b4895ad5495da3182ce2acdd7a6f"
  integrity sha1-GthgoZ2otIla1Uldoxgs4qzdem8=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jackspeak@^4.0.1:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/jackspeak/download/jackspeak-4.1.0.tgz"
  integrity sha1-xInAefK2NtxMvpsDEqE/8SguVhs=
  dependencies:
    "@isaacs/cliui" "^8.0.2"

jiti@^1.21.6:
  version "1.21.7"
  resolved "http://r.npm.sankuai.com/jiti/download/jiti-1.21.7.tgz"
  integrity sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/kleur/download/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-3.1.3.tgz"
  integrity sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash-es/download/lodash-es-4.17.21.tgz"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.sortedlastindex@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/lodash.sortedlastindex/download/lodash.sortedlastindex-4.1.0.tgz"
  integrity sha1-RWmHgZa0UNrGqrTOsIls7tSmclU=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-6.0.0.tgz"
  integrity sha1-u5Xl8FMiZRysMMD+tkBPnyqKlDk=
  dependencies:
    chalk "^5.3.0"
    is-unicode-supported "^1.3.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^11.0.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-11.1.0.tgz"
  integrity sha1-r6+wYGBxCBMtvBz4rmYa+2lIYRc=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

magic-string@^0.30.10, magic-string@^0.30.11, magic-string@^0.30.17:
  version "0.30.17"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

merge2@^1.3.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mimic-function@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/mimic-function/download/mimic-function-5.0.1.tgz"
  integrity sha1-rL4rM0n5m53qyn+3Dki4PpTmcHY=

minimatch@^10.0.0:
  version "10.0.1"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-10.0.1.tgz"
  integrity sha1-zgUhhWtFPIbiXyxMDQPm/33cRAs=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^3.1.1:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.5:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mitt@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/mitt/download/mitt-3.0.1.tgz#ea36cf0cc30403601ae074c8f77b7092cdab36d1"
  integrity sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=

mlly@^1.7.4:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/mlly/download/mlly-1.7.4.tgz"
  integrity sha1-PXKV6iNY7HonHqpdAAoPhP6+EA8=
  dependencies:
    acorn "^8.14.0"
    pathe "^2.0.1"
    pkg-types "^1.3.0"
    ufo "^1.5.4"

ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mz@^2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz"
  integrity sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=

node-fetch-native@^1.6.4:
  version "1.6.6"
  resolved "http://r.npm.sankuai.com/node-fetch-native/download/node-fetch-native-1.6.6.tgz"
  integrity sha1-rh0OU3rzXCwLDegcv/N+7dQQqjc=

node-html-parser@^6.1.13:
  version "6.1.13"
  resolved "http://r.npm.sankuai.com/node-html-parser/download/node-html-parser-6.1.13.tgz"
  integrity sha1-od95m4PfXGdD/NknQLoUaCCDt+Q=
  dependencies:
    css-select "^5.1.0"
    he "1.2.0"

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

nypm@^0.5.2:
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/nypm/download/nypm-0.5.4.tgz"
  integrity sha1-pasNjTf5Y0IyhHn4jvWGmfKbMFE=
  dependencies:
    citty "^0.1.6"
    consola "^3.4.0"
    pathe "^2.0.3"
    pkg-types "^1.3.1"
    tinyexec "^0.3.2"
    ufo "^1.5.4"

object-assign@^4.0.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/object-hash/download/object-hash-3.0.0.tgz"
  integrity sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=

ofetch@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/ofetch/download/ofetch-1.4.1.tgz"
  integrity sha1-tr9rDXW6YWzvZRndi2OFqLrkgOw=
  dependencies:
    destr "^2.0.3"
    node-fetch-native "^1.6.4"
    ufo "^1.5.4"

ohash@^2.0.11:
  version "2.0.11"
  resolved "http://r.npm.sankuai.com/ohash/download/ohash-2.0.11.tgz"
  integrity sha1-YLEejP9iyp3uiNE3R6W6oUX1kAs=

once@^1.3.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-7.0.0.tgz"
  integrity sha1-nxbJLYye9RIOOs2d2ZV8zuzBq2A=
  dependencies:
    mimic-function "^5.0.0"

ora@^8.2.0:
  version "8.2.0"
  resolved "http://r.npm.sankuai.com/ora/download/ora-8.2.0.tgz"
  integrity sha1-j7u3FRr+M7VA3RU/Fx/6i9OOmGE=
  dependencies:
    chalk "^5.3.0"
    cli-cursor "^5.0.0"
    cli-spinners "^2.9.2"
    is-interactive "^2.0.0"
    is-unicode-supported "^2.0.0"
    log-symbols "^6.0.0"
    stdin-discarder "^0.2.2"
    string-width "^7.2.0"
    strip-ansi "^7.1.0"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-browserify/download/path-browserify-1.0.1.tgz"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-scurry@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/path-scurry/download/path-scurry-2.0.0.tgz"
  integrity sha1-nwUiifI62L+Tl6KgQl57hhXFhYA=
  dependencies:
    lru-cache "^11.0.0"
    minipass "^7.1.2"

pathe@^2.0.1, pathe@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-2.0.3.tgz"
  integrity sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/perfect-debounce/download/perfect-debounce-1.0.0.tgz#9c2e8bc30b169cc984a58b7d5b28049839591d2a"
  integrity sha1-nC6LwwsWnMmEpYt9WygEmDlZHSo=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.2.tgz"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pify@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pinia@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/pinia/download/pinia-3.0.2.tgz#0616c2e1b39915f253c7626db3c81b7cdad695da"
  integrity sha1-BhbC4bOZFfJTx2Jts8gbfNrWldo=
  dependencies:
    "@vue/devtools-api" "^7.7.2"

pirates@^4.0.1:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/pirates/download/pirates-4.0.7.tgz"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

pkg-types@^1.3.0, pkg-types@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/pkg-types/download/pkg-types-1.3.1.tgz"
  integrity sha1-vXzHCIEZJ3fu9TJsGd60bokJF98=
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.4"
    pathe "^2.0.1"

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "http://r.npm.sankuai.com/postcss-import/download/postcss-import-15.1.0.tgz"
  integrity sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-js/download/postcss-js-4.0.1.tgz"
  integrity sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=
  dependencies:
    camelcase-css "^2.0.1"

postcss-less@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/postcss-less/download/postcss-less-6.0.0.tgz"
  integrity sha1-Rjs0xg9TtkjCN/VprrLgkUnYWvQ=

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-4.0.2.tgz"
  integrity sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/postcss-nested/download/postcss-nested-6.2.0.tgz"
  integrity sha1-TC0iq18gucth4sXFkVlQeE0GgTE=
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-sass@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/postcss-sass/download/postcss-sass-0.5.0.tgz"
  integrity sha1-pZlxfu+QFl0mfmqXTGbC7RU3a3c=
  dependencies:
    gonzales-pe "^4.3.0"
    postcss "^8.2.14"

postcss-scss@^4.0.9:
  version "4.0.9"
  resolved "http://r.npm.sankuai.com/postcss-scss/download/postcss-scss-4.0.9.tgz"
  integrity sha1-oDx3PNTJYjywTOFCpSr87HSAZoU=

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-styl@^0.12.3:
  version "0.12.3"
  resolved "http://r.npm.sankuai.com/postcss-styl/download/postcss-styl-0.12.3.tgz"
  integrity sha1-HXBayKCPt3rRX94LX5vWLbhYiVY=
  dependencies:
    debug "^4.1.1"
    fast-diff "^1.2.0"
    lodash.sortedlastindex "^4.1.0"
    postcss "^7.0.27 || ^8.0.0"
    stylus "^0.57.0"

postcss-value-parser@^4.0.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

"postcss@^7.0.27 || ^8.0.0", postcss@^8.2.14, postcss@^8.4.38, postcss@^8.4.47, postcss@^8.4.48, postcss@^8.5.2, postcss@^8.5.3:
  version "8.5.3"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prettier@^3.2.5:
  version "3.5.3"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-3.5.3.tgz"
  integrity sha1-T8LODWV+egLmAlSfBTsjnLff4bU=

prompts@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/prompts/download/prompts-2.4.2.tgz"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

read-cache@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/read-cache/download/read-cache-1.0.0.tgz"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

readdirp@^4.0.1:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-4.1.2.tgz"
  integrity sha1-64WAFDX78qfuWPGeCSGwaPxplI0=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

recast@^0.23.9:
  version "0.23.11"
  resolved "http://r.npm.sankuai.com/recast/download/recast-0.23.11.tgz"
  integrity sha1-iIVXC7KM93O6HcYA2n9QL3iD9z8=
  dependencies:
    ast-types "^0.16.1"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tiny-invariant "^1.3.3"
    tslib "^2.0.1"

reka-ui@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/reka-ui/download/reka-ui-2.2.1.tgz"
  integrity sha1-uYaAgL6fH5YP+n8IiRCAYagZADM=
  dependencies:
    "@floating-ui/dom" "^1.6.13"
    "@floating-ui/vue" "^1.1.6"
    "@internationalized/date" "^3.5.0"
    "@internationalized/number" "^3.5.0"
    "@tanstack/vue-virtual" "^3.12.0"
    "@vueuse/core" "^12.5.0"
    "@vueuse/shared" "^12.5.0"
    aria-hidden "^1.2.4"
    defu "^6.1.4"
    ohash "^2.0.11"

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/resolve-pkg-maps/download/resolve-pkg-maps-1.0.0.tgz"
  integrity sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=

resolve@^1.1.7, resolve@^1.22.8:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^5.0.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-5.1.0.tgz"
  integrity sha1-B2bZVpnvrLFBUJk/VbrwlT6h6+c=
  dependencies:
    onetime "^7.0.0"
    signal-exit "^4.1.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rfdc@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rollup@^4.34.9:
  version "4.40.2"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.40.2.tgz"
  integrity sha1-d46It6GXVCaCs+MYWB92l/VfBhk=
  dependencies:
    "@types/estree" "1.0.7"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.40.2"
    "@rollup/rollup-android-arm64" "4.40.2"
    "@rollup/rollup-darwin-arm64" "4.40.2"
    "@rollup/rollup-darwin-x64" "4.40.2"
    "@rollup/rollup-freebsd-arm64" "4.40.2"
    "@rollup/rollup-freebsd-x64" "4.40.2"
    "@rollup/rollup-linux-arm-gnueabihf" "4.40.2"
    "@rollup/rollup-linux-arm-musleabihf" "4.40.2"
    "@rollup/rollup-linux-arm64-gnu" "4.40.2"
    "@rollup/rollup-linux-arm64-musl" "4.40.2"
    "@rollup/rollup-linux-loongarch64-gnu" "4.40.2"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.40.2"
    "@rollup/rollup-linux-riscv64-gnu" "4.40.2"
    "@rollup/rollup-linux-riscv64-musl" "4.40.2"
    "@rollup/rollup-linux-s390x-gnu" "4.40.2"
    "@rollup/rollup-linux-x64-gnu" "4.40.2"
    "@rollup/rollup-linux-x64-musl" "4.40.2"
    "@rollup/rollup-win32-arm64-msvc" "4.40.2"
    "@rollup/rollup-win32-ia32-msvc" "4.40.2"
    "@rollup/rollup-win32-x64-msvc" "4.40.2"
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safer-buffer@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass@^1.87.0:
  version "1.87.0"
  resolved "http://r.npm.sankuai.com/sass/download/sass-1.87.0.tgz"
  integrity sha1-jM6zb6Y/tIqNXX8vTBO0nFJLcj4=
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

sax@~1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.6:
  version "7.7.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.1.tgz"
  integrity sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=

shadcn-vue@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/shadcn-vue/download/shadcn-vue-2.1.0.tgz"
  integrity sha1-F0lWHt3ypxEfo3lw4BVgieS5f2I=
  dependencies:
    "@unovue/detypes" "^0.8.5"
    "@vue/compiler-sfc" "^3.5"
    commander "^12.1.0"
    consola "^3.4.0"
    cosmiconfig "^9.0.0"
    deepmerge "^4.3.1"
    diff "^7.0.0"
    fs-extra "^11.3.0"
    get-tsconfig "^4.10.0"
    lodash-es "^4.17.21"
    magic-string "^0.30.17"
    nypm "^0.5.2"
    ofetch "^1.4.1"
    ora "^8.2.0"
    pathe "^2.0.3"
    pkg-types "^1.3.1"
    postcss "^8.5.2"
    prompts "^2.4.2"
    reka-ui "^2.0.0"
    stringify-object "^5.0.0"
    tailwindcss "^3.4.16"
    tinyexec "^0.3.2"
    tinyglobby "^0.2.10"
    ts-morph "^24.0.0"
    undici "^7.3.0"
    vue-metamorph "3.2.0"
    zod "^3.24.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/sisteransi/download/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-resolve@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/source-map-resolve/download/source-map-resolve-0.6.0.tgz"
  integrity sha1-PZ34fiNrU/FtAeWBUPx3EROOXtI=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"

source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.7.4.tgz"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

speakingurl@^14.0.1:
  version "14.0.1"
  resolved "http://r.npm.sankuai.com/speakingurl/download/speakingurl-14.0.1.tgz#f37ec8ddc4ab98e9600c1c9ec324a8c48d772a53"
  integrity sha1-837I3cSrmOlgDByewySoxI13KlM=

stdin-discarder@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/stdin-discarder/download/stdin-discarder-0.2.2.tgz"
  integrity sha1-OQA39ExK4aGuU1xf443Dq6jZl74=

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-7.2.0.tgz"
  integrity sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=
  dependencies:
    emoji-regex "^10.3.0"
    get-east-asian-width "^1.0.0"
    strip-ansi "^7.1.0"

stringify-object@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/stringify-object/download/stringify-object-5.0.0.tgz"
  integrity sha1-1bBWSf7a+IYGQEcWQfcJBv6n81E=
  dependencies:
    get-own-enumerable-keys "^1.0.0"
    is-obj "^3.0.0"
    is-regexp "^3.1.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

stylus@^0.57.0:
  version "0.57.0"
  resolved "http://r.npm.sankuai.com/stylus/download/stylus-0.57.0.tgz"
  integrity sha1-pG8E9CbBnO71SrsanRif1OiG30E=
  dependencies:
    css "^3.0.0"
    debug "^4.3.2"
    glob "^7.1.6"
    safer-buffer "^2.1.2"
    sax "~1.2.4"
    source-map "^0.7.3"

sucrase@^3.35.0:
  version "3.35.0"
  resolved "http://r.npm.sankuai.com/sucrase/download/sucrase-3.35.0.tgz"
  integrity sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

superjson@^2.2.2:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/superjson/download/superjson-2.2.2.tgz#9d52bf0bf6b5751a3c3472f1292e714782ba3173"
  integrity sha1-nVK/C/a1dRo8NHLxKS5xR4K6MXM=
  dependencies:
    copy-anything "^3.0.2"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

table@^6.8.2:
  version "6.9.0"
  resolved "http://r.npm.sankuai.com/table/download/table-6.9.0.tgz"
  integrity sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tailwindcss@^3.4.16:
  version "3.4.17"
  resolved "http://r.npm.sankuai.com/tailwindcss/download/tailwindcss-3.4.17.tgz"
  integrity sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

tiny-invariant@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/tiny-invariant/download/tiny-invariant-1.3.3.tgz"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tinyexec@^0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/tinyexec/download/tinyexec-0.3.2.tgz"
  integrity sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=

tinyglobby@^0.2.10, tinyglobby@^0.2.13, tinyglobby@^0.2.9:
  version "0.2.13"
  resolved "http://r.npm.sankuai.com/tinyglobby/download/tinyglobby-0.2.13.tgz"
  integrity sha1-oORlFc5svNZTMVN+V0hK9aey/34=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz"
  integrity sha1-eE/T1nlyK8EDsbS4AwvN212yppk=

ts-morph@^24.0.0:
  version "24.0.0"
  resolved "http://r.npm.sankuai.com/ts-morph/download/ts-morph-24.0.0.tgz"
  integrity sha1-Ykm1Jq3kDPmciAPnq9rmxliC5Y4=
  dependencies:
    "@ts-morph/common" "~0.25.0"
    code-block-writer "^13.0.3"

tslib@^2.0.0, tslib@^2.0.1, tslib@^2.8.0:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

typescript@^5.4.5:
  version "5.8.3"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-5.8.3.tgz"
  integrity sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=

ufo@^1.5.4:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/ufo/download/ufo-1.6.1.tgz"
  integrity sha1-rC2x1UYU0bIsHWA+Ou9EqF2PFGs=

undici@^7.3.0:
  version "7.8.0"
  resolved "http://r.npm.sankuai.com/undici/download/undici-7.8.0.tgz"
  integrity sha1-z1GFSmyyaAipxqP7LFjBPJTRAIE=

universalify@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

vite@^6.3.5:
  version "6.3.5"
  resolved "http://r.npm.sankuai.com/vite/download/vite-6.3.5.tgz"
  integrity sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=
  dependencies:
    esbuild "^0.25.0"
    fdir "^6.4.4"
    picomatch "^4.0.2"
    postcss "^8.5.3"
    rollup "^4.34.9"
    tinyglobby "^0.2.13"
  optionalDependencies:
    fsevents "~2.3.3"

vue-demi@>=0.13.0:
  version "0.14.10"
  resolved "http://r.npm.sankuai.com/vue-demi/download/vue-demi-0.14.10.tgz"
  integrity sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=

vue-eslint-parser@^9.4.3:
  version "9.4.3"
  resolved "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-9.4.3.tgz"
  integrity sha1-mwSyLHFAHx6Lypvnw+NBakvedqg=
  dependencies:
    debug "^4.3.4"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.6"

vue-metamorph@3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/vue-metamorph/download/vue-metamorph-3.2.0.tgz"
  integrity sha1-AArFkHMhA7zWuYVDKXfksCqdeSo=
  dependencies:
    "@babel/parser" "8.0.0-alpha.12"
    ast-types "^0.14.2"
    chalk "^5.3.0"
    cli-progress "^3.12.0"
    commander "^12.1.0"
    deep-diff "^1.0.2"
    fs-extra "^11.2.0"
    glob "^11.0.0"
    lodash-es "^4.17.21"
    magic-string "^0.30.10"
    micromatch "^4.0.8"
    node-html-parser "^6.1.13"
    postcss "^8.4.38"
    postcss-less "^6.0.0"
    postcss-sass "^0.5.0"
    postcss-scss "^4.0.9"
    postcss-styl "^0.12.3"
    recast "^0.23.9"
    table "^6.8.2"
    vue-eslint-parser "^9.4.3"

vue-router@^4.5.1:
  version "4.5.1"
  resolved "http://r.npm.sankuai.com/vue-router/download/vue-router-4.5.1.tgz"
  integrity sha1-R7/+LTpUedKIapokRUeoU6oKv2k=
  dependencies:
    "@vue/devtools-api" "^6.6.4"

vue@^3.5.13:
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/vue/download/vue-3.5.13.tgz"
  integrity sha1-n3YKGpgrCcDASoZ5A/wznJ8p7Ao=
  dependencies:
    "@vue/compiler-dom" "3.5.13"
    "@vue/compiler-sfc" "3.5.13"
    "@vue/runtime-dom" "3.5.13"
    "@vue/server-renderer" "3.5.13"
    "@vue/shared" "3.5.13"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@^2.3.4:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-2.7.1.tgz"
  integrity sha1-RKJH0biFI4VWeax/p82m7X4TXPY=

zod@^3.24.2:
  version "3.24.4"
  resolved "http://r.npm.sankuai.com/zod/download/zod-3.24.4.tgz"
  integrity sha1-4uLMpfqqAS125SfQ02Yi4KkMMV8=
