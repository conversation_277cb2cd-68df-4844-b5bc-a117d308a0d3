body {
  font-family: 'ZCOOL KuaiLe', 'Noto Serif <PERSON>', serif, 'Pixelify Sans', monospace, sans-serif;
  background-color: var(--color-ancient-paper);
  color: var(--color-ancient-dark-brown);
}

// 古风像素容器样式
.ancient-pixel-container {
  background-color: var(--color-ancient-paper); // 纸张色
  border: 3px solid var(--color-ancient-dark-brown); // 深色边框
  border-radius: 0; // 锐利边缘
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown); // 像素化阴影
  padding: 25px;
  margin: 20px;
  position: relative; // For internal positioning
}

// 像素风格按钮样式
.pixel-button {
  background-color: var(--color-ancient-gold); // 金色
  color: var(--color-ancient-ink); // 墨色
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0; // 锐利边缘
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown); // 像素阴影
  font-family: 'Pixelify Sans', monospace; // 像素字体

  &:hover {
    transform: translate(-2px, -2px); // 悬停上移
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-gold-light); // 使用预定义的亮色变量
  }

  &:active {
    transform: translate(2px, 2px); // 点击下移
    box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
  }

  &:disabled {
    background-color: var(--color-ancient-stone-gray);
    color: var(--color-neutral-white); // White text on disabled
    border-color: var(--color-ancient-stone-gray-dark);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    opacity: 0.8;
  }

  // 辅助类，用于强调或不同颜色
  &.primary {
    background-color: var(--color-ancient-jade);
    color: var(--color-ancient-ink);
    border-color: var(--color-ancient-jade-dark);
    &:hover { background-color: var(--color-ancient-jade-light); }
  }
  &.danger {
    background-color: var(--color-ancient-blood-red);
    color: var(--color-neutral-white);
    border-color: var(--color-ancient-blood-red-dark);
    &:hover { background-color: var(--color-ancient-blood-red-light); }
  }
}


// 像素风格输入框样式
.pixel-input {
  width: 100%;
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0; // 锐利边缘
  padding: 10px 15px; // 调整内边距
  font-size: 15px;
  background-color: var(--color-ancient-paper); // 背景色与纸张色一致
  color: var(--color-ancient-ink);
  transition: border-color 0.3s, box-shadow 0.3s;
  margin-bottom: 15px;
  font-family: 'Pixelify Sans', monospace; // 像素字体

  &:focus {
    outline: none;
    border-color: var(--color-ancient-jade); // 焦点时边框变色
    box-shadow: 2px 2px 0px var(--color-ancient-jade); // 像素化焦点阴影
  }

  &::placeholder {
    color: var(--color-ancient-light-brown); // 浅棕色占位符
  }
}

// 输入框图标容器 (保持通用，但图标需要是像素风格的)
.input-icon-container {
  position: relative;

  .input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-ancient-light-brown);
    font-size: 18px;
    font-family: 'Pixelify Sans', monospace; // 确保图标占位符也用像素字体
  }
  // If you want padding for icon inside the input
  .pixel-input.with-icon {
    padding-left: 45px; // Make space for icon
  }
}

// 标签切换样式
.tab-switcher { // Renamed for clarity
  display: flex;
  margin-bottom: 20px;
  border-radius: 0; // 锐利边缘
  overflow: hidden;
  background-color: var(--color-ancient-stone-gray); // 灰色背景
  border: 2px solid var(--color-ancient-dark-brown); // 边框

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: bold;
    font-family: 'Pixelify Sans', monospace; // 像素字体
    color: var(--color-ancient-ink);

    &.active {
      background-color: var(--color-ancient-gold); // 金色选中背景
      color: var(--color-ancient-ink);
      transform: scale(1.02); // 轻微放大
      box-shadow: inset 0 0 0 2px var(--color-ancient-dark-brown); // 内边框阴影
    }

    &:not(.active):hover {
      background-color: var(--color-ancient-stone-gray-light); // 悬停效果
    }
  }
}

// 像素风格复选框
.pixel-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 15px;
  font-family: 'Pixelify Sans', monospace; // 像素字体
  color: var(--color-ancient-dark-brown);

  input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    width: 20px; // 稍小
    height: 20px;
    border: 2px solid var(--color-ancient-dark-brown); // 边框
    border-radius: 0; // 锐利
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    background-color: var(--color-ancient-paper); // 背景色

    &:checked {
      background-color: var(--color-ancient-jade); // 选中时翡翠绿
      border-color: var(--color-ancient-jade-dark);

      &:after {
        content: '';
        position: absolute;
        left: 5px; // 调整勾的位置
        top: 2px;
        width: 6px;
        height: 10px;
        border: solid var(--color-ancient-ink); // 墨色勾
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }
  }
}

// 背景装饰元素（像素化/古风化）
// 移除原有的 .cloud, .mountains 样式。
// 对于像素风格背景，通常需要图片拼接或更复杂的CSS绘制。
// 这里仅提供概念性说明和建议，实际实现需准备像素艺术图片。
/*
.background-pixel-clouds {
  // Example: Use background-image with repeating pixel patterns
  background-image: url('/path/to/pixel-cloud-tile.png');
  background-repeat: repeat-x;
  position: absolute;
  top: 0; left: 0; width: 100%; height: 100px;
  z-index: 0;
  // Apply pixelated image-rendering here if needed
}

.background-ancient-mountains {
  // Example: Use background-image with a larger, static pixel art mountain range
  background-image: url('/path/to/pixel-mountain-range.png');
  background-repeat: no-repeat;
  background-position: bottom center;
  background-size: cover; // or specific size
  position: absolute;
  bottom: 0; left: 0; width: 100%; height: 200px;
  z-index: 0;
  // Apply pixelated image-rendering here if needed
}
*/

// 其他通用文本/提示样式
.ancient-text { // A general class for text that should follow the ancient theme
  font-family: 'Noto Serif SC', serif;
  color: var(--color-ancient-dark-brown);
}

// 添加动画效果（保持像素风格的“跳跃”感）
@keyframes pixel-float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); } // 更小的浮动
  100% { transform: translateY(0px); }
}

.floating-pixel {
  animation: pixel-float 3s ease-in-out infinite; // 稍微快一点
}

@keyframes pixel-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  50% { transform: translateX(3px); }
  75% { transform: translateX(-3px); }
}

.shaking-pixel {
  animation: pixel-shake 0.3s ease-in-out;
}