<template>
  <header class="app-header">
    <div class="header-left">
      <!-- Logo Area -->
      <div class="logo" @click="goToDashboard">
        <!-- Placeholder for Pixel/Ancient Mascot Icon -->
        <span class="mascot-icon">🐉</span> <!-- Using a dragon emoji as placeholder -->
        <span class="logo-text">云端工位</span>
      </div>
    </div>
    <div class="header-center">
      <!-- Navigation Menu -->
      <nav class="main-nav">
        <ul>
          <li v-for="item in navItems" :key="item.name">
            <router-link :to="item.path" active-class="active-link" class="nav-link pixel-button-nav">
              <span class="nav-icon">{{ item.iconPlaceholder }}</span> <!-- Icon Placeholder -->
              <span class="nav-text">{{ item.name }}</span>
            </router-link>
          </li>
        </ul>
      </nav>
    </div>
    <div class="header-right">
      <!-- Function Area -->
      <div class="function-area">
        <!-- Message Notification -->
        <div class="notification-area">
          <span class="notification-icon">📜</span> <!-- Scroll emoji for notification -->
          <!-- New message indicator (example: use v-if based on data) -->
          <span class="notification-indicator"></span>
          <!-- Notification Dropdown Placeholder -->
          <!-- <div class="notification-dropdown">...</div> -->
        </div>

        <!-- User Area -->
        <div class="user-area">
          <div class="user-dropdown" @click="toggleDropdown">
            <div class="user-avatar-small">
              <img :src="headerAvatarUrl" alt="用户头像" class="avatar-img pixelated-image">
            </div>
            <!-- Simple Dropdown Menu -->
            <div class="dropdown-menu" v-if="isDropdownOpen">
              <ul>
                <li><router-link to="/workspace" class="ancient-text">我的工位</router-link></li>
                <li><a href="#" @click.prevent="logout" class="ancient-text">归隐</a></li>
              </ul>
            </div>
          </div>
          <!-- Optionally display username next to avatar -->
          <!-- <span class="user-nickname">{{ userInfo?.username || '用户' }}</span> -->
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
// axios is not directly used here, but imported in the original. Keeping it commented out.
// import axios from 'axios';
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);
const headerAvatarUrl = computed(() => userStore.fullAvatarUrl);

// Update navItems icons to be more pixel/ancient style
const navItems = ref([
  { name: '我的工位', path: '/dashboard', iconPlaceholder: '🏛️' }, // Temple
  { name: '修行日志', path: '/attendance', iconPlaceholder: '🕰️' }, // Hourglass/ancient clock
  { name: '榜文任务', path: '/tasks', iconPlaceholder: '⚔️' }, // Sword
  { name: '江湖茶馆', path: '/community', iconPlaceholder: '🏮' }, // Lantern/Teahouse
  { name: '江湖宝库', path: '/store', iconPlaceholder: '💎' }, // Gem/Treasure
]);

onMounted(() => {
  if (!userStore.userInfo) {
    userStore.fetchUserInfo();
  }
});

const logout = () => {
  userStore.logout();
  router.push({ name: 'Login' });
};

const goToDashboard = () => {
  router.push('/dashboard');
};

const isDropdownOpen = ref(false);
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};
</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // Ensure the new SCSS is imported

/* 基础头部样式 */
.app-header {
  background-color: var(--color-ancient-paper); /* 纸张色 */
  color: var(--color-ancient-dark-brown); /* 深棕色 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 3px solid var(--color-ancient-dark-brown); /* 底部边框 */
  box-shadow: 0 5px 0px var(--color-ancient-light-brown); /* 像素阴影 */
  flex-wrap: wrap;
  min-height: 60px;
  font-family: 'Pixelify Sans', monospace; // 头部整体字体
}

.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.header-left {
  margin-right: 20px;
  flex-grow: 0;
}

.header-center {
  flex-grow: 1;
  justify-content: center;
}

.header-right {
  margin-left: 20px;
  flex-grow: 0;
}

.logo {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 20px;
}

.mascot-icon {
  font-size: 30px; /* 增大图标 */
  margin-right: 8px;
}

.logo-text {
  font-size: 22px; /* 增大标题 */
  font-weight: bold;
  font-family: "ZCOOL KuaiLe", serif; // Logo文字使用古风字体
  color: var(--color-ancient-ink);
}

/* 导航样式 */
.main-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.main-nav li {
  margin: 5px 10px;
}

.nav-link {
  text-decoration: none;
  color: inherit;
  padding: 8px 12px; // 调整内边距
  border-radius: 0; // 锐利边缘
  transition: background-color 0.2s, transform 0.1s, box-shadow 0.2s;
  border: 1px solid transparent; // Default transparent border
  display: flex;
  align-items: center;

  &.pixel-button-nav { // Apply button-like styles
    background-color: transparent; // Start transparent
    color: var(--color-ancient-dark-brown);
    border: 2px solid transparent;
    box-shadow: none;

    &:hover {
      background-color: var(--color-ancient-highlight); // Highlight on hover
      border-color: var(--color-ancient-dark-brown);
      transform: translate(-1px, -1px);
      box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
    }
  }
}

.nav-link.active-link {
  font-weight: bold;
  background-color: var(--color-ancient-jade); // Active link background
  color: var(--color-neutral-white); // Active link text color
  border-color: var(--color-ancient-jade-dark);
  box-shadow: 2px 2px 0px var(--color-ancient-dark-brown);
  transform: translate(-1px, -1px); // Slightly "pressed" look
}

.nav-icon {
  font-size: 1.2em;
  margin-right: 4px;
}

/* 功能区域样式 */
.function-area {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.notification-area {
  position: relative;
  margin-right: 20px;
  cursor: pointer;
}

.notification-icon {
  font-size: 24px; // 增大图标
  color: var(--color-ancient-dark-brown);
}

.notification-indicator {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 8px;
  height: 8px;
  background-color: var(--color-ancient-blood-red); // 红色提示点
  border-radius: 50%;
  border: 1px solid var(--color-ancient-dark-brown); // 像素化边框
}

/* 用户区域样式 */
.user-area {
  position: relative;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar-small {
  width: 40px; // 增大头像尺寸
  height: 40px;
  border-radius: 0; // 锐利方形
  border: 2px solid var(--color-ancient-dark-brown);
  overflow: hidden;
  background-color: var(--color-ancient-light-brown);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  image-rendering: pixelated; // Ensure pixelation for avatars
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--color-ancient-paper); /* 纸张色 */
  border: 2px solid var(--color-ancient-dark-brown); /* 边框 */
  border-radius: 0;
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown); // 像素阴影
  min-width: 120px;
  z-index: 100;
  padding: 5px 0;
  font-family: 'Pixelify Sans', monospace;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    padding: 0;
    margin: 0;
    a, .router-link-active { // Target router-link as well
      display: block;
      padding: 8px 15px;
      text-decoration: none;
      color: var(--color-ancient-dark-brown);
      transition: background-color 0.2s, color 0.2s;

      &:hover {
        background-color: var(--color-ancient-highlight); // 悬停背景
        color: var(--color-ancient-ink); // 悬停文字颜色
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-header {
    padding: 10px;
    justify-content: center;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
    justify-content: center;
    margin: 5px 0;
  }

  .header-center {
    order: 3;
    flex-grow: 0;
  }

  .header-right {
     order: 2;
     margin-left: 0;
  }

  .header-left {
      order: 1;
      margin-right: 0;
  }

  .logo {
    justify-content: center;
  }

  .main-nav ul {
    flex-direction: column;
    align-items: center;
  }

  .main-nav li {
    margin: 5px 0;
  }

  .function-area {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 18px;
  }

  .mascot-icon {
    font-size: 24px;
  }

  .nav-link {
     padding: 5px 10px;
     font-size: 0.9em;
  }

  .nav-icon {
    font-size: 1em;
  }

  .notification-icon {
    font-size: 20px;
  }

  .user-avatar-small {
    width: 32px;
    height: 32px;
  }
}
</style>