import { createRouter, createWebHistory } from 'vue-router'

// 导入组件（这些组件我们稍后会创建）
const Login = () => import('../views/Login.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const Workspace = () => import('../views/Workspace.vue')
const Tasks = () => import('../views/Tasks.vue')
const Attendance = () => import('../views/Attendance.vue')
const Community = () => import('../views/Community.vue')
const Store = () => import('../views/Store.vue')

// 定义路由
const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/workspace',
    name: 'Workspace',
    component: Workspace,
    meta: { requiresAuth: true }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: Tasks,
    meta: { requiresAuth: true }
  },
  {
    path: '/attendance',
    name: 'Attendance',
    component: Attendance,
    meta: { requiresAuth: true }
  },
  {
    path: '/community',
    name: 'Community',
    component: Community,
    meta: { requiresAuth: true }
  },
  {
    path: '/store',
    name: 'Store',
    component: Store,
    meta: { requiresAuth: true }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫，用于验证用户是否已登录
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('token') // 简单检查是否有token
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要登录但未登录，重定向到登录页
    next({ name: 'Login' })
  } else {
    // 已登录或不需要登录的页面
    next()
  }
})

export default router 