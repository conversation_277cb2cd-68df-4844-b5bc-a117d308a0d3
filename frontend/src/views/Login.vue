<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();
const activeTab = ref('login');
const errorMessage = ref('');

const loginEmailOrUsername = ref('');
const loginPassword = ref('');
const rememberMe = ref(false);
const loginLoading = ref(false);

const registerUsername = ref('');
const registerEmail = ref('');
const registerPassword = ref('');
const registerConfirmPassword = ref('');
const registerLoading = ref(false);

const handleLogin = async () => {
  errorMessage.value = '';
  loginLoading.value = true;

  try {
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      emailOrUsername: loginEmailOrUsername.value,
      password: loginPassword.value,
    });

    if (response.data && response.data.token) {
      userStore.setToken(response.data.token);
      userStore.setUserInfo(response.data.user);

      localStorage.setItem('token', response.data.token);
      localStorage.setItem('userInfo', JSON.stringify(response.data.user));

      router.push({ name: 'Dashboard' });
    } else {
      errorMessage.value = response.data.message || '登录失败，请检查用户名/邮箱和密码。';
    }
  } catch (error) {
    console.error('登录请求出错:', error);
    errorMessage.value = error.response?.data?.message || '登录请求失败，请稍后重试。';
  } finally {
    loginLoading.value = false;
  }
};

const handleRegister = async () => {
  errorMessage.value = '';
  registerLoading.value = true;
  try {
    const response = await axios.post('http://localhost:3001/api/auth/signup', {
      username: registerUsername.value,
      email: registerEmail.value,
      password: registerPassword.value,
      confirmPassword: registerConfirmPassword.value,
    });
    if (response.data && response.data.success) {
      alert('铸就根骨，成功入世！请登录。'); // New: Registration success message
      activeTab.value = 'login'; // 注册成功后切换到登录页面
      // Clear register form
      registerUsername.value = '';
      registerEmail.value = '';
      registerPassword.value = '';
      registerConfirmPassword.value = '';
    } else {
      errorMessage.value = response.data.message || '入世失败，请稍后再试。'; // New: Registration failure message
    }
  } catch (error) {
    console.error('注册请求出错:', error);
    errorMessage.value = error.response?.data?.message || '入世请求失败，请稍后重试。';
  } finally {
    registerLoading.value = false;
  }
};

// No need for currentForm computed property if directly using individual refs.
</script>

<template>
  <div class="login-page ancient-pixel-background">
    <!-- 装饰性元素 - 像素化云朵 (Conceptual, may need image assets) -->
    <div class="pixel-cloud pixel-cloud-1 floating-pixel"></div>
    <div class="pixel-cloud pixel-cloud-2 floating-pixel"></div>
    <div class="pixel-cloud pixel-cloud-3 floating-pixel"></div>

    <!-- 装饰性元素 - 古风山丘 (Conceptual, may need image assets) -->
    <div class="ancient-mountains">
      <div class="mountain-pixel mountain-pixel-1"></div>
      <div class="mountain-pixel mountain-pixel-2"></div>
      <div class="mountain-pixel mountain-pixel-3"></div>
    </div>

    <!-- 主容器 -->
    <div class="login-container ancient-pixel-container card">
      <!-- Logo 部分 -->
      <div class="logo-container">
        <h1 class="app-title">云端工位</h1>
        <p class="slogan ancient-text">—— 江湖归宿，修身养性之所</p>
      </div>

      <!-- 插图 Placeholder (描述为像素风) -->
      <div class="illustration-placeholder">
        <span class="illustration-icon">⚔️</span> <!-- Sword emoji as placeholder -->
        <p class="ancient-text">仗剑走天涯，摸鱼也逍遥</p>
      </div>

      <!-- 登录/注册切换标签 -->
      <div class="tab-switcher"> <!-- Changed class name -->
        <div
          class="tab-item"
          :class="{ active: activeTab === 'login' }"
          @click="activeTab = 'login'"
        >
          入世
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'register' }"
          @click="activeTab = 'register'"
        >
          铸骨
        </div>
      </div>

      <!-- 错误消息 -->
      <div v-if="errorMessage" class="error-message shaking-pixel">
        {{ errorMessage }}
      </div>

      <!-- 登录表单 -->
      <form v-if="activeTab === 'login'" @submit.prevent="handleLogin" class="login-form">
        <div class="input-icon-container">
          <span class="input-icon">👤</span> <!-- User icon -->
          <input
            type="text"
            v-model="loginEmailOrUsername"
            placeholder="英雄名或侠客邮"
            class="pixel-input with-icon"
            required
          />
        </div>

        <div class="input-icon-container">
          <span class="input-icon">🔒</span> <!-- Lock icon -->
          <input
            type="password"
            v-model="loginPassword"
            placeholder="江湖通行令"
            class="pixel-input with-icon"
            required
          />
        </div>

        <div class="form-options">
          <label class="pixel-checkbox"> <!-- Changed class name -->
            <input type="checkbox" v-model="rememberMe">
            <span>铭刻印记</span>
          </label>

          <a href="#" class="forgot-password ancient-text">通行令遗失？</a>
        </div>

        <button
          type="submit"
          class="pixel-button primary"
          :disabled="loginLoading"
        >
          {{ loginLoading ? '悟道中...' : '🚀 仗剑入江湖！' }}
        </button>
      </form>

      <!-- 注册表单 -->
      <form v-else @submit.prevent="handleRegister" class="register-form">
        <div class="input-icon-container">
          <span class="input-icon">📜</span> <!-- Scroll icon -->
          <input
            type="text"
            v-model="registerUsername"
            placeholder="赐我一个响亮的名号"
            class="pixel-input with-icon"
            required
          />
        </div>

        <div class="input-icon-container">
          <span class="input-icon">✉️</span> <!-- Mail icon -->
          <input
            type="email"
            v-model="registerEmail"
            placeholder="通报江湖的邮箱"
            class="pixel-input with-icon"
            required
          />
        </div>

        <div class="input-icon-container">
          <span class="input-icon">🔑</span> <!-- Key icon -->
          <input
            type="password"
            v-model="registerPassword"
            placeholder="设一道入江湖的秘令"
            class="pixel-input with-icon"
            required
          />
        </div>

         <div class="input-icon-container">
            <span class="input-icon">🔑</span> <!-- Key icon -->
            <input
              type="password"
              v-model="registerConfirmPassword"
              placeholder="再确认一次秘令"
              class="pixel-input with-icon"
              required
            />
         </div>

        <button
          type="submit"
          class="pixel-button primary"
          :disabled="registerLoading"
        >
          {{ registerLoading ? '筑基中...' : '🎉 归入山门！' }}
        </button>
      </form>

      <!-- 第三方登录 -->
      <div class="third-party-login" v-if="activeTab === 'login'">
        <p class="ancient-text">或从其他门派登录</p>
        <div class="third-party-icons">
          <div class="third-party-icon">
            <span>🥷</span> <!-- Ninja emoji as placeholder -->
          </div>
          <div class="third-party-icon">
            <span>🐉</span> <!-- Dragon emoji as placeholder -->
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="copyright ancient-text">
        © {{ new Date().getFullYear() }} 云端工位 · 江湖归宿，修身养性之所
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.login-page {
  min-height: 100vh;
  background-color: var(--color-ancient-paper); /* 纸张背景色 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  font-family: 'Pixelify Sans', monospace; /* 页面通用字体 */
  color: var(--color-ancient-dark-brown);
}

/* Background Decorations - Pixel Clouds (Conceptual) */
.pixel-cloud {
  position: absolute;
  width: 100px; /* 像素化云朵尺寸 */
  height: 40px;
  background-color: #f0f8ff; /* 浅蓝白色 */
  border-radius: 0; /* 锐利边缘 */
  box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.2); /* 像素化阴影 */
  opacity: 0.8;
  z-index: 0;
  // For actual pixel art clouds, use background-image: url('pixel-cloud.png'); background-size: contain; image-rendering: pixelated;
  &:before, &:after { // Simplified cloud shape for pixel effect
      content: '';
      position: absolute;
      background-color: inherit;
      width: 70%;
      height: 70%;
      border-radius: 0;
      box-shadow: inherit;
  }
  &:before { top: -20%; left: 20%; }
  &:after { bottom: -20%; right: 20%; }
}

.pixel-cloud-1 { top: 10%; left: 5%; width: 150px; height: 50px; animation-duration: 6s; }
.pixel-cloud-2 { top: 20%; right: 10%; width: 120px; height: 45px; animation-duration: 7s; animation-direction: alternate-reverse; }
.pixel-cloud-3 { bottom: 15%; left: 15%; width: 180px; height: 60px; animation-duration: 8s; }

/* Background Decorations - Ancient Pixel Mountains (Conceptual) */
.ancient-mountains {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    overflow: hidden;
    z-index: 0;
    // For actual pixel art mountains, use a background image:
    // background-image: url('/path/to/pixel-mountain-background.png');
    // background-repeat: no-repeat;
    // background-position: bottom center;
    // background-size: cover;
    // image-rendering: pixelated;
}

.mountain-pixel {
    position: absolute;
    bottom: 0;
    border-radius: 0; /* 锐利边缘 */
    background-color: #92B591; /* 深绿色 */
    box-shadow: 4px 4px 0px #7FA07E; /* 像素阴影 */
    opacity: 0.8; /* 稍透明 */

    &:nth-child(1) {
      width: 300px;
      height: 250px;
      left: -80px;
      bottom: -150px;
      transform: skewX(-15deg); /* 模拟山体形状 */
    }

    &:nth-child(2) {
      width: 400px;
      height: 350px;
      left: 30%;
      bottom: -250px;
      background-color: #7AA87C; /* 稍浅绿色 */
      transform: translateX(-50%) skewX(10deg);
    }

    &:nth-child(3) {
      width: 350px;
      height: 300px;
      right: -100px;
      bottom: -200px;
      background-color: #6C946C; /* 更深绿色 */
      transform: skewX(-5deg);
    }
}


/* Login Container (Card Style - using ancient-pixel-container) */
.login-container { // Using the global .ancient-pixel-container defined in SCSS
  z-index: 1;
  width: 100%;
  max-width: 450px; // Slightly larger for better ancient feel
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 25px;
  padding: 40px 30px;
}

/* Logo Section */
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.app-title {
  font-family: "ZCOOL KuaiLe", serif; // 古风字体
  font-size: 38px; // 更大
  color: var(--color-ancient-ink); // 墨色
  margin: 0;
  text-shadow: 3px 3px 0px var(--color-ancient-dark-brown); // 像素化文字阴影
}

.slogan {
    font-size: 16px;
    color: var(--color-ancient-light-brown);
    margin-top: 0;
}

/* Illustration Placeholder */
.illustration-placeholder {
  text-align: center;
  margin-bottom: 20px;
  .illustration-icon {
    font-size: 60px; // 巨大的图标
    display: block;
    margin-bottom: 10px;
    color: var(--color-ancient-jade); // 翡翠绿
    text-shadow: 2px 2px 0px var(--color-ancient-dark-brown);
  }
  p {
    font-size: 16px;
    color: var(--color-ancient-dark-brown);
  }
}


/* Tab Container (Login/Register Switch) - using tab-switcher */
.tab-switcher { // Inherits styles from ancient-pixel.scss
  width: 100%;
  margin-bottom: 25px;
}

/* Error Message */
.error-message {
  width: 100%;
  padding: 10px;
  background-color: #ffe0b2; /* 浅橙色背景 */
  color: var(--color-ancient-blood-red); /* 血红色文字 */
  border: 2px solid var(--color-ancient-blood-red); /* 边框 */
  border-radius: 0;
  text-align: center;
  font-family: 'Pixelify Sans', monospace;
  font-size: 14px;
  margin-top: -15px;
  margin-bottom: 15px;
  box-shadow: 3px 3px 0px #D9C085;
}

/* Form Styles (Login and Register) */
.login-form,
.register-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px; // 调整间距
}

.input-icon-container {
  display: flex;
  align-items: center;
  background-color: var(--color-ancient-paper); // 与卡片背景一致
  border: 2px solid var(--color-ancient-dark-brown); // 边框
  border-radius: 0;
  padding: 8px 10px; // 调整内边距
  gap: 10px;
  box-shadow: 2px 2px 0px var(--color-ancient-light-brown); // 像素阴影
  transition: box-shadow 0.3s ease-in-out;
}

.input-icon {
  font-size: 20px; // 增大图标
  color: var(--color-ancient-light-brown);
  flex-shrink: 0;
}

.pixel-input { // Inherits from ancient-pixel.scss
  flex-grow: 1;
  font-size: 16px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'Pixelify Sans', monospace;
  font-size: 14px;
}

.pixel-checkbox { // Inherits from ancient-pixel.scss
    span {
        color: var(--color-ancient-dark-brown);
    }
}

.forgot-password {
  color: var(--color-ancient-jade); // 翡翠绿
  text-decoration: none;
  font-weight: bold;
  &:hover {
      text-decoration: underline;
      color: var(--color-ancient-jade-dark);
  }
}

/* Buttons - using pixel-button classes */
.pixel-button { // Inherits from ancient-pixel.scss
  width: 100%; // Make buttons full width
  margin-top: 10px;
}


/* Third Party Login */
.third-party-login {
  margin-top: 25px;
  text-align: center;
  width: 100%;

  p {
    color: var(--color-ancient-light-brown);
    margin-bottom: 15px;
    font-size: 14px;
    font-family: 'Noto Serif SC', serif;
  }

  .third-party-icons {
    display: flex;
    justify-content: center;
    gap: 20px;

    .third-party-icon {
      width: 50px;
      height: 50px;
      border-radius: 0; // 锐利
      background-color: var(--color-ancient-paper);
      border: 2px solid var(--color-ancient-dark-brown);
      color: var(--color-ancient-dark-brown);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.3s, box-shadow 0.3s;
      font-family: 'Pixelify Sans', monospace;
      font-size: 24px; // 增大图标
      font-weight: bold;
      box-shadow: 3px 3px 0px var(--color-ancient-light-brown);

      &:hover {
        transform: translate(-2px, -2px) scale(1.05);
        box-shadow: 5px 5px 0px var(--color-ancient-light-brown);
      }
    }
  }
}

/* Copyright Info */
.copyright {
  margin-top: 25px;
  text-align: center;
  font-size: 12px;
  color: var(--color-ancient-light-brown);
  font-family: 'Noto Serif SC', serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .login-container {
    padding: 30px 20px;
  }

  .app-title {
      font-size: 32px;
  }

  .slogan {
      font-size: 14px;
  }

  .illustration-placeholder .illustration-icon {
      font-size: 50px;
  }

  .tab-item {
      font-size: 14px;
      padding: 8px 15px;
  }

  .pixel-input {
      font-size: 14px;
  }

  .form-options {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
  }

  .third-party-login .third-party-icon {
      width: 45px;
      height: 45px;
      font-size: 20px;
  }
}

@media (max-width: 480px) {
     .login-container {
       padding: 20px 15px;
     }

     .app-title {
         font-size: 28px;
     }

     .slogan {
         font-size: 12px;
     }

     .illustration-placeholder .illustration-icon {
         font-size: 40px;
     }

     .tab-item {
         font-size: 12px;
         padding: 6px 10px;
     }

     .pixel-input {
         font-size: 12px;
         padding: 6px 8px;
     }

     .form-options {
         font-size: 12px;
     }

      .pixel-button {
          font-size: 16px;
          padding: 10px 15px;
      }

     .third-party-login {
          p {
              font-size: 12px;
          }
          .third-party-icons {
              gap: 15px;
              .third-party-icon {
                   width: 35px;
                   height: 35px;
                   font-size: 18px;
              }
          }
     }

     .copyright {
         font-size: 10px;
     }
}
</style>