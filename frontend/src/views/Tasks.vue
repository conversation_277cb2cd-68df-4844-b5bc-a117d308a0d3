<template>
  <div class="tasks-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>榜文任务 · 江湖历练</h1>

    <!-- Top Filter/Category Tags -->
    <div class="filter-tags">
      <button
        v-for="tag in filterTags"
        :key="tag.value"
        class="tag-button"
        :class="{ 'active': activeTag === tag.value }"
        @click="setActiveTag(tag.value)"
      >
        {{ tag.label }}
      </button>
    </div>

    <!-- Task List Area -->
    <div v-if="loading" class="loading-indicator">榜文载入中...</div>
    <div v-else-if="error" class="error-text">{{ error }}</div>
    <div v-else class="task-list-container">
      <div
        v-for="task in tasks"
        :key="task._id"
        class="card task-card"
        :class="getTaskStatusClass(task.status)"
        @click="viewTaskDetail(task._id)"
      >
        <div class="task-icon">{{ getTaskIcon(task.category) }}</div>
        <div class="task-details">
          <div class="task-title">{{ task.title }}</div>
          <div class="task-description">{{ task.description }}</div>
        </div>
        <div class="task-meta">
          <div class="task-reward">💰 +{{ task.rewardPoints }} 内力值</div>
          <div class="task-status-display">状态: <span :class="getTaskStatusClass(task.status)">{{ getTaskStatusText(task.status) }}</span></div>
          <button
            class="claim-button pixel-button primary"
            :class="{ 'claimed': task.status !== 'pending' && task.status !== 'in_progress', 'status-completed': task.status === 'completed' }"
            @click.stop="handleTaskAction(task)"
            :disabled="isTaskActionButtonDisabled(task)"
          >
            {{ getTaskActionButtonText(task) }}
          </button>
        </div>
      </div>

      <div v-if="tasks.length === 0 && !loading" class="empty-tasks ancient-text">
          <p>当前分类下没有榜文哦，去看看别的吧！</p>
      </div>
    </div>

    <!-- 任务详情模态框 -->
    <div v-if="selectedTask" class="modal-overlay" @click.self="closeTaskDetailModal">
      <div class="modal-content card">
        <button class="modal-close-button" @click="closeTaskDetailModal">X</button>
        <div v-if="loadingTaskDetail" class="loading-indicator">榜文详情载入中...</div>
        <div v-else-if="taskDetailError" class="error-text">{{ taskDetailError }}</div>
        <div v-else-if="selectedTask">
          <h2 class="task-detail-title">{{ selectedTask.title }}</h2>
          <p class="task-detail-meta">
            发布者: {{ selectedTask.creator?.nickname || selectedTask.creator?.username || '佚名' }} |
            发布时间: {{ new Date(selectedTask.createdAt).toLocaleDateString() }}
          </p>
          <div class="task-detail-body">
            <p>任务描述: {{ selectedTask.description }}</p>
            <p>奖励内力值: 💰 {{ selectedTask.rewardPoints }}</p>
            <p v-if="selectedTask.assignee">领取者: {{ selectedTask.assignee?.nickname || selectedTask.assignee?.username || '佚名' }}</p>
            <p>截止日期: {{ selectedTask.dueDate ? new Date(selectedTask.dueDate).toLocaleDateString() : '无限制' }}</p>
            <p>当前状态: <span :class="getTaskStatusClass(selectedTask.status)">{{ getTaskStatusText(selectedTask.status) }}</span></p>
          </div>

          <div v-if="actionSuccessMessage" class="success-message">{{ actionSuccessMessage }}</div>
          <div v-if="actionError" class="error-message">{{ actionError }}</div>

          <div class="task-detail-actions">
            <button
              v-if="selectedTask.status === 'pending'"
              @click="claimTask(selectedTask._id)"
              class="pixel-button primary"
              :disabled="submittingAction || !isLoggedIn"
            >
              {{ submittingAction ? '领取中...' : '领取榜文' }}
            </button>
            <button
              v-if="selectedTask.status === 'in_progress' && selectedTask.assignee?._id === currentUser._id"
              @click="completeTask(selectedTask._id)"
              class="pixel-button primary"
              :disabled="submittingAction"
            >
              {{ submittingAction ? '完成中...' : '完成榜文' }}
            </button>
            <button
              v-if="selectedTask.status === 'in_progress' && selectedTask.assignee?._id !== currentUser._id"
              class="pixel-button secondary"
              disabled
            >
              已被他人领取
            </button>
            <button
              v-if="selectedTask.status === 'completed'"
              class="pixel-button secondary status-completed"
              disabled
            >
              已完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useTaskStore } from '@/stores/task';
import { useUserStore } from '@/stores/user'; // 引入 userStore 获取当前用户ID
import { storeToRefs } from 'pinia';

const taskStore = useTaskStore();
const userStore = useUserStore(); // 初始化 userStore

const {
  tasks,
  loading,
  error,
  selectedTask,
  loadingTaskDetail,
  taskDetailError,
  submittingAction,
  actionError,
  actionSuccessMessage,
  getTaskStatusText,
  getTaskStatusClass,
  getTaskActionButtonText,
  isTaskActionButtonDisabled
} = storeToRefs(taskStore);

const { isLoggedIn, userInfo: currentUser } = storeToRefs(userStore); // 获取登录状态和当前用户信息

// Filter tags
const filterTags = ref([
  { label: '全部榜文', value: 'all' },
  { label: '门派推荐', value: 'public' }, // 映射到后端isPublic
  { label: '我领取的', value: 'my-tasks' }, // 映射到后端assignedToMe
]);
const activeTag = ref('all');

// Function to set active tag and refetch tasks
const setActiveTag = (tagValue) => {
  activeTag.value = tagValue;
  let filters = {};
  if (tagValue === 'public') {
    filters.isPublic = true;
  } else if (tagValue === 'my-tasks') {
    filters.assignedToMe = true;
  }
  taskStore.fetchTasks(filters);
};

// Map task categories to icons
const getTaskIcon = (category) => {
  switch (category) {
    case 'work': return '💼'; // Briefcase
    case 'study': return '📚'; // Books
    case 'design': return '🎨'; // Art palette
    case 'development': return '💻'; // Laptop
    case 'document': return '📄'; // Document
    case 'other': return '✨'; // Sparkles
    default: return '❓'; // Question mark
  }
};


// Task Detail Modal Logic
const viewTaskDetail = async (taskId) => {
  await taskStore.fetchTaskById(taskId);
};

const closeTaskDetailModal = () => {
  taskStore.clearSelectedTask();
};

// Task Actions (Claim / Complete)
const handleTaskAction = async (task) => {
  if (!isLoggedIn.value) {
    alert('请先入世修行，方可领取或完成榜文！'); // Consider routing to login
    return;
  }

  if (task.status === 'pending') {
    const result = await taskStore.claimTask(task._id);
    if (result.success) {
      // 可以在此处显示一个短暂的成功提示
      console.log('领取成功!');
      // 模态框打开的情况下，自动刷新模态框里的任务状态
      if (selectedTask.value && selectedTask.value._id === task._id) {
          await taskStore.fetchTaskById(task._id);
      }
    } else {
      console.error('领取失败:', result.message);
    }
  } else if (task.status === 'in_progress' && task.assignee?._id === currentUser.value._id) {
    const result = await taskStore.completeTask(task._id);
    if (result.success) {
      console.log('完成成功!');
      // 模态框打开的情况下，自动刷新模态框里的任务状态
      if (selectedTask.value && selectedTask.value._id === task._id) {
          await taskStore.fetchTaskById(task._id);
      }
    } else {
      console.error('完成失败:', result.message);
    }
  }
};


// Modal action functions
const claimTask = async (taskId) => {
  const result = await taskStore.claimTask(taskId);
  if (result.success) {
    console.log('领取成功!');
  } else {
    console.error('领取失败:', result.message);
  }
};

const completeTask = async (taskId) => {
  const result = await taskStore.completeTask(taskId);
  if (result.success) {
    console.log('完成成功!');
  } else {
    console.error('完成失败:', result.message);
  }
};

onMounted(() => {
  taskStore.fetchTasks(); // Fetch all tasks on mount
});

// Watch for currentUser changes to refetch tasks when login status changes
watch(currentUser, (newVal, oldVal) => {
  // Only refetch if login status actually changed (e.g., from null to user object, or vice-versa)
  if (newVal?._id !== oldVal?._id) {
    taskStore.fetchTasks();
  }
});

</script>

<style scoped lang="scss">
// Remove the import since we're defining styles directly here

.tasks-page {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  background-color: var(--color-ancient-paper);
}

h1 {
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
  font-size: 2em;
  font-family: 'ZCOOL KuaiLe', serif;
}

.filter-tags {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tag-button {
  // Base pixel button styles (copied from .pixel-button)
  background-color: var(--color-ancient-paper); // Override default gold
  color: var(--color-ancient-dark-brown);
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0;
  padding: 8px 15px; // Override default padding
  font-size: 0.9em; // Override default font size
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
  font-family: 'Pixelify Sans', monospace;

  &:hover {
    background-color: var(--color-ancient-highlight);
    transform: translate(-1px, -1px);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }
}

.tag-button.active {
  background-color: var(--color-ancient-jade); // Active tag background
  color: var(--color-ancient-ink); // Active tag text color
  border-color: var(--color-ancient-jade-dark);
  box-shadow: 4px 4px 0px var(--color-ancient-dark-brown);
}

.loading-indicator, .error-text {
  text-align: center;
  color: var(--color-ancient-light-brown);
  font-style: italic;
  padding: 20px 0;
  font-family: 'Noto Serif SC', serif;
}

.error-text {
  color: var(--color-ancient-blood-red);
  font-weight: bold;
}

.task-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.task-card {
  // Base card styles (copied from global .card)
  padding: 2em;
  background-color: var(--color-ancient-paper);
  border-radius: 0;
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);

  // Task-specific styles
  display: flex;
  align-items: flex-start;
  gap: 15px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  position: relative;
  padding: 20px; // Override the 2em padding from card

  &:hover {
    transform: translate(-3px, -3px);
    box-shadow: 9px 9px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-highlight);
  }

  // Task Status Specific Styles
  &.status-pending {
    border-color: var(--color-ancient-gold);
    .task-icon { color: var(--color-ancient-gold); }
  }
  &.status-in-progress {
    border-color: var(--color-ancient-jade);
    .task-icon { color: var(--color-ancient-jade); }
  }
  &.status-completed {
    border-color: var(--color-ancient-stone-gray);
    background-color: var(--color-ancient-stone-gray-light);
    color: var(--color-ancient-stone-gray-dark);
    cursor: default;
    .task-icon { color: var(--color-ancient-stone-gray); }
    .task-title, .task-description {
      text-decoration: line-through;
      color: var(--color-ancient-stone-gray-dark);
    }
    .claim-button {
      background-color: var(--color-ancient-stone-gray) !important; // Override primary/secondary
      color: #ffffff !important; // Use explicit white instead of undefined variable
      border-color: var(--color-ancient-stone-gray-dark) !important;
      box-shadow: none !important;
      transform: none !important;
      cursor: not-allowed !important;
      &:hover { opacity: 0.9; }
    }
  }
  &.status-cancelled {
    border-color: var(--color-ancient-blood-red);
    background-color: #fcebeb; // Lighter red background
    color: var(--color-ancient-blood-red-dark);
    cursor: default;
    .task-icon { color: var(--color-ancient-blood-red); }
    .task-title, .task-description {
      text-decoration: line-through;
      color: var(--color-ancient-blood-red-dark);
    }
     .claim-button {
      background-color: var(--color-ancient-blood-red) !important;
      color: #ffffff !important; // Use explicit white instead of undefined variable
      border-color: var(--color-ancient-blood-red-dark) !important;
      box-shadow: none !important;
      transform: none !important;
      cursor: not-allowed !important;
      &:hover { opacity: 0.9; }
    }
  }
}

.task-icon {
  font-size: 36px;
  flex-shrink: 0;
  line-height: 1; // Prevent extra spacing
}

.task-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-family: 'Noto Serif SC', serif;
}

.task-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 1.1em;
  font-weight: bold;
  color: var(--color-ancient-ink);
}

.task-description {
  font-family: 'Noto Serif SC', serif;
  font-size: 0.9em;
  color: var(--color-ancient-dark-brown);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;
  font-family: 'Pixelify Sans', monospace;
  text-align: right; // Align all meta text right
}

.task-reward,
.task-status-display {
  font-size: 0.9em;
  color: var(--color-ancient-dark-brown);
  display: flex;
  align-items: center;
  justify-content: flex-end; // Align content within flex item to the right
  width: 100%; // Take full width of meta container

  span {
    margin-left: 5px; // Space after text for icon
    font-weight: bold;
  }
  .status-pending { color: var(--color-ancient-gold); }
  .status-in-progress { color: var(--color-ancient-jade); }
  .status-completed { color: #888; } // Lighter gray for completed text
  .status-cancelled { color: var(--color-ancient-blood-red); }
}

.claim-button {
  // Base pixel button styles (copied from .pixel-button)
  background-color: var(--color-ancient-gold);
  color: var(--color-ancient-ink);
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  font-family: 'Pixelify Sans', monospace;

  // Custom styles for claim button
  font-size: 0.9em;
  padding: 8px 15px;
  width: 100%;
  white-space: nowrap;

  &:hover:not(:disabled) {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-gold-light);
  }

  &:active:not(:disabled) {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
  }

  &:disabled {
    background-color: var(--color-ancient-stone-gray);
    color: #ffffff; // Use explicit white instead of undefined variable
    border-color: var(--color-ancient-stone-gray-dark);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    opacity: 0.8;
  }

  // Primary variant (for jade colored buttons)
  &.primary {
    background-color: var(--color-ancient-jade);
    color: var(--color-ancient-ink);
    border-color: var(--color-ancient-jade-dark);
    &:hover:not(:disabled) {
      background-color: var(--color-ancient-jade-light);
    }
  }

  // Secondary variant (for gray buttons)
  &.secondary {
    background-color: var(--color-ancient-stone-gray);
    color: #ffffff; // Use explicit white instead of undefined variable
    border-color: var(--color-ancient-stone-gray-dark);
    &:hover:not(:disabled) {
      background-color: var(--color-ancient-stone-gray-light);
    }
  }
}

.empty-tasks {
  text-align: center;
  color: var(--color-ancient-light-brown);
  padding: 40px 20px;
  grid-column: 1 / -1; // Span across all columns in grid
  font-family: 'Noto Serif SC', serif;
}

/* Modal Styles (Task Detail) */
.modal-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.modal-content {
  // Base card styles (copied from global .card)
  padding: 2em;
  background-color: var(--color-ancient-paper);
  border-radius: 0;
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);

  // Modal-specific styles
  padding: 30px; // Override the 2em padding from card
  width: 90%;
  max-width: 600px;
  position: relative;
  max-height: 90vh;
  overflow-y: auto;

  .modal-close-button {
    position: absolute;
    top: 10px; right: 10px;
    background: none; border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--color-ancient-dark-brown);
    &:hover { color: #555; }
  }

  .task-detail-title {
    font-family: 'ZCOOL KuaiLe', serif;
    font-size: 1.8em;
    color: var(--color-ancient-ink);
    margin-top: 0;
    margin-bottom: 10px;
    text-align: center;
  }

  .task-detail-meta {
    font-family: 'Pixelify Sans', monospace;
    font-size: 0.9em;
    color: var(--color-ancient-light-brown);
    margin-bottom: 20px;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 10px;
    text-align: center;
  }

  .task-detail-body {
    font-family: 'Noto Serif SC', serif;
    font-size: 1em;
    color: var(--color-ancient-dark-brown);
    line-height: 1.6;
    margin-bottom: 20px;

    p { margin-bottom: 8px; }
    span { font-weight: bold; color: var(--color-ancient-ink); }
    .status-pending { color: var(--color-ancient-gold); }
    .status-in-progress { color: var(--color-ancient-jade); }
    .status-completed { color: #888; }
    .status-cancelled { color: var(--color-ancient-blood-red); }
  }

  .task-detail-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
  }
   .success-message, .error-message {
      text-align: center;
      padding: 10px;
      margin-bottom: 15px;
      border-radius: 0;
      font-size: 0.9em;
      font-family: 'Pixelify Sans', monospace;
      border-width: 2px;
      border-style: solid;
   }
   .success-message {
       color: var(--color-ancient-jade-dark);
       background-color: var(--color-ancient-paper);
       border-color: var(--color-ancient-jade);
   }
   .error-message {
       color: var(--color-ancient-blood-red);
       background-color: var(--color-ancient-paper);
       border-color: var(--color-ancient-blood-red);
   }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tasks-page {
    padding: 15px;
  }
  h1 { font-size: 1.8em; text-align: center; }
  .filter-tags { justify-content: center; }
  .tag-button { padding: 6px 12px; font-size: 0.8em; }
  .task-list-container { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
  .task-card { flex-direction: column; align-items: flex-start; gap: 10px; padding: 15px; }
  .task-icon { font-size: 30px; }
  .task-details { width: 100%; }
  .task-title { font-size: 1em; }
  .task-description { font-size: 0.8em; }
  .task-meta { flex-direction: row; justify-content: space-between; align-items: center; gap: 10px; width: 100%; }
  .claim-button { padding: 6px 12px; font-size: 0.8em; }
  .modal-content { max-width: 450px; padding: 20px; }
  .task-detail-title { font-size: 1.5em; }
  .task-detail-meta, .task-detail-body { font-size: 0.9em; }
}

@media (max-width: 480px) {
  .tasks-page { padding: 10px; }
  h1 { font-size: 1.5em; margin-bottom: 15px; }
  .task-list-container { grid-template-columns: 1fr; }
  .task-card { padding: 10px; }
  .task-icon { font-size: 24px; }
  .task-meta { flex-direction: column; align-items: flex-start; gap: 5px; }
  .claim-button { font-size: 0.85em; padding: 5px 10px; }
  .modal-content { padding: 15px; }
  .task-detail-title { font-size: 1.2em; }
  .task-detail-meta, .task-detail-body { font-size: 0.85em; }
  .task-detail-actions { gap: 8px; }
  .modal-content .pixel-button { font-size: 0.9em; padding: 8px 12px; }
}
</style>