<template>
  <div class="attendance-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>修行日志 · 考勤回顾</h1>

    <!-- 考勤概览统计模块 -->
    <div class="card attendance-summary-module">
      <h2 class="card-title">📊 修行概览</h2>
      <div v-if="loadingStats" class="loading-text">卷宗载入中...</div>
      <div v-else-if="statsError" class="error-text">{{ statsError }}</div>
      <div v-else class="stats-grid">
        <div class="stat-item">
            <span class="stat-label">今日修行时常:</span>
            <span class="stat-value">{{ todayHours }}时</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月走火入魔:</span>
            <span class="stat-value">{{ lateCount }}次</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月闭关天数:</span>
            <span class="stat-value">{{ leaveCount }}天</span>
        </div>
      </div>
    </div>

    <!-- 我的打卡日历模块 -->
    <div class="card attendance-calendar-card">
      <h2 class="card-title">📅 我的修行日历</h2>
      <div v-if="loadingMonthlyRecords" class="loading-text">日历卷宗载入中...</div>
      <div v-else-if="monthlyRecordsError" class="error-text">{{ monthlyRecordsError }}</div>
      <div v-else class="calendar-content">
        <div class="calendar-header">
          <button @click="changeMonth(-1)" class="pixel-button">&lt; 前一月</button>
          <span class="current-month-year">{{ currentYear }}年 {{ currentMonth + 1 }}月</span>
          <button @click="changeMonth(1)" class="pixel-button">后一月 &gt;</button>
        </div>
        <div class="weekdays">
          <span v-for="day in weekdays" :key="day">{{ day }}</span>
        </div>
        <div class="calendar-grid">
          <div
            v-for="(day, index) in calendarDays"
            :key="index"
            class="calendar-day"
            :class="{
              'is-today': day.isToday,
              'has-records': day.hasRecords,
              'is-current-month': day.isCurrentMonth,
              'is-selected': selectedDate && day.date && day.date.toDateString() === selectedDate.toDateString()
            }"
            @click="selectDay(day)"
          >
            <span v-if="day.dayOfMonth !== null" class="day-number">{{ day.dayOfMonth }}</span>
            <div v-if="day.hasRecords" class="record-indicator">
              <!-- 显示迟到或早退的简短指示 -->
              <span v-if="day.status.includes('late')" class="status-indicator late" title="迟到">走</span>
              <span v-if="day.status.includes('early')" class="status-indicator early" title="早退">早</span>
            </div>
          </div>
        </div>
        <!-- 日期详情显示 -->
        <div v-if="selectedDateDetails" class="selected-date-details ancient-pixel-container detail-card">
            <h3 class="card-title">
              {{ selectedDateDetails.dateString }} 修行详情
            </h3>
            <div v-if="selectedDateDetails.records.length > 0">
                <div v-for="record in selectedDateDetails.records" :key="record._id" class="detail-record-item">
                    <span class="record-type" :style="{ color: getRecordStatusDisplay(record.type === 'in' ? record.status : '').color }">
                        {{ record.type === 'in' ? '入定' : '出定' }}
                    </span>
                    <span class="record-time">{{ record.time }}</span>
                    <span v-if="record.type === 'out' && record.hoursWorked > 0" class="record-hours">(修行: {{ record.hoursWorked }}时)</span>
                    <span class="record-status" :style="{ color: getRecordStatusDisplay(record.status).color }">
                        [{{ getRecordStatusDisplay(record.status).text }}]
                    </span>
                </div>
            </div>
            <p v-else class="empty-text">今日无修行记录。</p>
        </div>
      </div>
    </div>

    <!-- 历史打卡记录模块 -->
    <div class="card attendance-records-card">
      <h2 class="card-title">📜 历史修行卷宗</h2>
      <div v-if="loadingMonthlyRecords" class="loading-text">卷宗载入中...</div>
      <div v-else-if="monthlyRecordsError" class="error-text">{{ monthlyRecordsError }}</div>
      <div v-else class="records-list">
        <div v-if="allClockIns.length === 0" class="empty-records">
          <p class="ancient-text">此卷宗暂无记录。</p>
        </div>
        <div v-else class="record-item" v-for="record in allClockIns" :key="record._id">
          <div class="record-date ancient-text">{{ record.dateString }}</div>
          <div class="record-details">
            <p class="ancient-text">
                {{ record.type === 'in' ? '入定' : '出定' }}：{{ record.time }}
                <span v-if="record.type === 'out' && record.hoursWorked > 0" class="record-hours-worked">(修行: {{ record.hoursWorked }}时)</span>
                <span class="record-status" :style="{ color: getRecordStatusDisplay(record.status).color }">
                  [{{ getRecordStatusDisplay(record.status).text }}]
                </span>
            </p>
            <p v-if="record.note" class="record-note">备注：{{ record.note }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useAttendanceStore } from '@/stores/attendance';
import { storeToRefs } from 'pinia';

const attendanceStore = useAttendanceStore();

const {
  clockedIn,
  clockInTime,
  clockOutTime,
  todayHours,
  lateCount, // Now represents monthlyLateCount
  leaveCount,
  loadingStats,
  statsError,
  monthlyRecords, // New: monthly records for calendar
  loadingMonthlyRecords,
  monthlyRecordsError,
  getRecordStatusDisplay // New getter for status display
} = storeToRefs(attendanceStore);

// Calendar State
const currentMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());
const selectedDate = ref(null); // The date selected in the calendar

const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// Helper to format date to YYYY-MM-DD
const formatDateToYYYYMMDD = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Computed property for calendar days
const calendarDays = computed(() => {
  const days = [];
  const firstDayOfMonth = new Date(currentYear.value, currentMonth.value, 1);
  const lastDayOfMonth = new Date(currentYear.value, currentMonth.value + 1, 0);
  const numDaysInMonth = lastDayOfMonth.getDate();
  const firstDayOfWeek = firstDayOfMonth.getDay(); // 0 for Sunday, 1 for Monday...

  // Add leading empty days for previous month
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push({ dayOfMonth: null, date: null, hasRecords: false, isCurrentMonth: false, status: [] });
  }

  // Add days of the current month
  const today = new Date();
  const todayYYYYMMDD = formatDateToYYYYMMDD(today);

  for (let i = 1; i <= numDaysInMonth; i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);
    const dateString = formatDateToYYYYMMDD(date);
    const recordsForDay = monthlyRecords.value.filter(
      (r) => r.dateString === dateString
    );
    const hasRecords = recordsForDay.length > 0;
    const isToday = dateString === todayYYYYMMDD;

    // Collect all unique statuses for the day
    const dayStatuses = new Set();
    recordsForDay.forEach(record => {
      if (record.status && record.status !== 'normal') { // Only track 'late', 'early', 'makeup' for display on calendar
        dayStatuses.add(record.status);
      }
    });

    days.push({
      dayOfMonth: i,
      date: date,
      dateString: dateString,
      hasRecords: hasRecords,
      isToday: isToday,
      isCurrentMonth: true,
      records: recordsForDay, // Attach records for detail view
      status: Array.from(dayStatuses) // Array of statuses for the day
    });
  }

  // Add trailing empty days for next month to fill the grid (total 42 cells for 6 weeks)
  const remainingCells = 42 - days.length;
  for (let i = 0; i < remainingCells; i++) {
    days.push({ dayOfMonth: null, date: null, hasRecords: false, isCurrentMonth: false, status: [] });
  }

  return days;
});

// Computed property for selected date details
const selectedDateDetails = computed(() => {
  if (!selectedDate.value) {
    // 如果没有手动选择日期，默认显示今天的记录（如果有）
    const today = new Date();
    const todayYYYYMMDD = formatDateToYYYYMMDD(today);
    const todayRecords = monthlyRecords.value.filter(r => r.dateString === todayYYYYMMDD);
    if (todayRecords.length > 0) {
      return {
        dateString: todayYYYYMMDD,
        records: [...todayRecords].sort((a,b) => new Date(a.date) - new Date(b.date)) // 确保今日记录按时间排序
      };
    }
    return null; // 今天也没有记录
  }
  const dateString = formatDateToYYYYMMDD(selectedDate.value);
  const records = monthlyRecords.value.filter(r => r.dateString === dateString);
  return { dateString, records: [...records].sort((a,b) => new Date(a.date) - new Date(b.date)) }; // 确保选中日期记录按时间排序
});


// All clock-ins for the selected month (for the list below calendar)
const allClockIns = computed(() => {
  // Sort by date and then by time for chronological order
  return [...monthlyRecords.value].sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    // Sort by date descending
    if (dateA.toDateString() !== dateB.toDateString()) {
      return dateB.getTime() - dateA.getTime();
    }
    // For same date, sort by time ascending
    const timeA = new Date(`2000/01/01 ${a.time}`); // Use arbitrary date for time comparison
    const timeB = new Date(`2000/01/01 ${b.time}`);
    return timeA.getTime() - timeB.getTime();
  });
});


// Calendar actions
const changeMonth = (offset) => {
  currentMonth.value += offset;
  if (currentMonth.value < 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else if (currentMonth.value > 11) {
    currentMonth.value = 0;
    currentYear.value++;
  }
  selectedDate.value = null; // Reset selected date when changing month
  attendanceStore.fetchMonthlyRecords(currentYear.value, currentMonth.value + 1);
};

const selectDay = (day) => {
  if (day.dayOfMonth !== null && day.isCurrentMonth) {
    selectedDate.value = day.date;
  }
};


onMounted(() => {
  attendanceStore.fetchAttendanceData(); // Fetch summary stats (today's status, monthly late count)
  attendanceStore.fetchMonthlyRecords(currentYear.value, currentMonth.value + 1); // Fetch initial month records for calendar/list
});

// Watch monthlyRecords to update selectedDateDetails if it's currently showing today and records change
watch(monthlyRecords, (newRecords) => {
  // If no specific date is manually selected, and today has records, select today
  if (!selectedDate.value) {
    const today = new Date();
    const todayYYYYMMDD = formatDateToYYYYMMDD(today);
    const todayRecords = newRecords.filter(r => r.dateString === todayYYYYMMDD);
    if (todayRecords.length > 0) {
      selectedDate.value = today;
    }
  } else {
    // If a date was already selected, ensure its details are refreshed based on new monthlyRecords
    const currentlySelectedDateString = formatDateToYYYYMMDD(selectedDate.value);
    const newRecordsForSelectedDate = newRecords.filter(r => r.dateString === currentlySelectedDateString);
    if (newRecordsForSelectedDate.length > 0) {
        // Force re-evaluation of selectedDateDetails computed property by changing selectedDate reference
        selectedDate.value = new Date(selectedDate.value);
    } else {
        selectedDate.value = null; // If selected date no longer has records, deselect
    }
  }
});


</script>

<style scoped lang="scss">
// Ensure ancient-pixel.scss variables are available
@use '../styles/ancient-pixel.scss' as ap;

.attendance-page {
  padding: 20px; // Keep base padding
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
}

h1 {
  color: var(--color-ancient-ink); // Deep ink color
  margin-bottom: 25px; // More space after title
  text-align: left;
  font-size: 2em;
}

.card {
  margin-bottom: 20px; // Space between cards
}

.card-title {
  font-size: 1.5em;
  color: var(--color-ancient-ink);
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px dashed var(--color-ancient-light-brown);
  padding-bottom: 10px;
  font-family: 'ZCOOL KuaiLe', serif;
}

.loading-text, .error-text, .empty-records {
  text-align: center;
  color: var(--color-ancient-light-brown);
  font-style: italic;
  padding: 10px 0;
  font-family: 'Noto Serif SC', serif;
}

.error-text {
  color: var(--color-ancient-blood-red);
  font-weight: bold;
}

/* 考勤概览统计模块 */
.attendance-summary-module {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    padding: 10px 0;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    border-radius: 0;
    box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
    padding: 15px;
    text-align: center;
    font-family: 'Pixelify Sans', monospace;
  }

  .stat-label {
    font-size: 0.9em;
    color: var(--color-ancient-dark-brown);
    margin-bottom: 5px;
  }

  .stat-value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--color-ancient-gold); // Highlight stats with gold
    text-shadow: 1px 1px 0px var(--color-ancient-dark-brown);
  }
}

/* 我的打卡日历模块 */
.attendance-calendar-card {
  .calendar-content {
    padding: 15px;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }

  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-family: 'Pixelify Sans', monospace;
    font-weight: bold;
    color: var(--color-ancient-ink);

    .pixel-button { // Apply general pixel-button styles
        // Directly extend the class from ancient-pixel.scss
        @extend .pixel-button;
        padding: 5px 10px;
        font-size: 0.9em;
    }
  }

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: bold;
    color: var(--color-ancient-dark-brown);
    margin-bottom: 10px;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 5px;
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
  }

  .calendar-day {
    position: relative;
    padding: 10px 5px;
    border: 1px solid var(--color-ancient-stone-gray-light);
    border-radius: 0;
    text-align: center;
    cursor: pointer;
    background-color: var(--color-ancient-paper);
    min-height: 60px; // Ensure consistent height
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    &:hover:not(.is-empty):not(.is-today) {
        background-color: var(--color-ancient-highlight);
        transform: translate(-1px, -1px);
        box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
    }
    &.is-empty { // Days from prev/next month
        background-color: var(--color-ancient-stone-gray-light);
        color: var(--color-ancient-stone-gray-dark);
        opacity: 0.6;
        cursor: default;
    }
    &.is-current-month {
        color: var(--color-ancient-dark-brown);
    }
    &.is-today {
        border: 2px solid var(--color-ancient-jade);
        background-color: var(--color-ancient-jade-light);
        font-weight: bold;
        color: var(--color-ancient-ink);
        box-shadow: 2px 2px 0px var(--color-ancient-jade-dark);
    }
    &.has-records {
        font-weight: bold;
    }
    &.is-selected {
        border: 2px solid var(--color-ancient-gold);
        background-color: var(--color-ancient-gold-light);
        box-shadow: 2px 2px 0px var(--color-ancient-gold);
        color: var(--color-ancient-ink);
    }

    .day-number {
      font-size: 1.1em;
      margin-bottom: 5px;
    }

    .record-indicator {
      display: flex;
      gap: 3px;
      margin-top: auto; // Push to bottom
      span {
        font-size: 0.7em;
        font-weight: bold;
        padding: 2px 4px;
        border-radius: 0;
        border: 1px solid currentColor; // Border same color as text
        line-height: 1;
      }
      .late {
        color: var(--color-ancient-blood-red);
      }
      .early {
        color: orange; // Placeholder color for early
      }
      // Add other statuses like 'makeup' if needed
    }
  }

  .selected-date-details {
    margin-top: 20px;
    padding: 15px;
    @extend .ancient-pixel-container; // Inherit card styles
    .card-title {
        font-size: 1.2em;
        margin-bottom: 10px;
    }
    .detail-record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px dashed var(--color-ancient-stone-gray-light);
        &:last-child {
            border-bottom: none;
        }
        span {
            font-size: 0.9em;
        }
        .record-type {
            font-weight: bold;
        }
        .record-status {
            font-weight: bold;
        }
    }
  }
}

/* 历史打卡记录模块 */
.attendance-records-card {
  .records-list {
    padding: 10px 0;
  }

  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 1px solid var(--color-ancient-light-brown);
    border-radius: 0;
    padding: 10px 15px;
    margin-bottom: 8px;
    box-shadow: 1px 1px 0px var(--color-ancient-light-brown);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .record-date {
    font-size: 1.1em;
    font-weight: bold;
    color: var(--color-ancient-ink);
    flex-shrink: 0;
    margin-right: 15px;
  }

  .record-details {
    flex-grow: 1;
    text-align: right;
    p {
      margin: 3px 0;
      font-size: 0.9em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Pixelify Sans', monospace;
    }
    .record-hours-worked {
      font-weight: bold;
      color: var(--color-ancient-jade);
    }
    .record-status {
      font-weight: bold;
    }
  }
  .empty-records {
    a {
      color: var(--color-ancient-jade);
      text-decoration: underline;
      &:hover {
        color: var(--color-ancient-jade-dark);
      }
    }
  }
}


/* 响应式调整 */
@media (max-width: 768px) {
  .attendance-page {
    padding: 15px;
  }

  h1 {
    font-size: 1.8em;
    text-align: center;
  }

  .card-title {
    font-size: 1.3em;
  }

  .attendance-summary-module .stats-grid {
    grid-template-columns: 1fr; /* Stack on smaller screens */
  }

  .attendance-summary-module .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px;
    .stat-value {
      font-size: 1.2em;
    }
  }

  .attendance-calendar-card .calendar-day {
      min-height: 40px;
      padding: 5px;
      .day-number { font-size: 0.9em; }
      .record-indicator {
          span { font-size: 0.6em; padding: 1px 3px; }
      }
  }

  .selected-date-details {
      .detail-record-item {
          span { font-size: 0.8em; }
      }
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    .record-details {
      width: 100%;
      text-align: left;
    }
  }
}

@media (max-width: 480px) {
   .attendance-page {
      padding: 10px;
   }
    h1 {
      font-size: 1.5em;
      margin-bottom: 15px;
    }

   .card-title {
      font-size: 1.1em;
   }

   .attendance-summary-module .stat-item {
      padding: 8px 12px;
      .stat-label {
        font-size: 0.8em;
      }
      .stat-value {
        font-size: 1em;
      }
   }

   .attendance-calendar-card {
       .calendar-header {
           .pixel-button { font-size: 0.8em; padding: 3px 6px; }
           .current-month-year { font-size: 0.9em; }
       }
       .weekdays span { font-size: 0.8em; }
       .calendar-day {
           min-height: 30px;
           padding: 3px;
           .day-number { font-size: 0.8em; }
           .record-indicator {
               span { font-size: 0.5em; padding: 1px 2px; }
           }
       }
   }

   .selected-date-details {
       padding: 10px;
       .card-title { font-size: 1em; }
       .detail-record-item {
           span { font-size: 0.7em; }
       }
   }

   .record-item {
      padding: 8px 10px;
      .record-date {
        font-size: 1em;
      }
      .record-details p {
        font-size: 0.8em;
      }
   }
}
</style>