<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAttendanceStore } from '@/stores/attendance';
import { useTaskStore } from '@/stores/task'; // **新增：引入 taskStore**
import { storeToRefs } from 'pinia';

const router = useRouter();
const userStore = useUserStore();
const attendanceStore = useAttendanceStore();
const taskStore = useTaskStore(); // **新增：初始化 taskStore**

// User Info from userStore
const userInfo = computed(() => userStore.userInfo);
const loadingUser = computed(() => userStore.loading);
const userError = computed(() => userStore.error);
const fullAvatarUrl = computed(() => userStore.fullAvatarUrl);

// Attendance Info from attendanceStore
const {
  clockedIn,
  clockInTime,
  clockOutTime,
  isClockingIn,
  todayHours,
  loadingStats: loadingAttendance,
  statsError: attendanceError,
} = storeToRefs(attendanceStore);

// **新增：Task Info from taskStore**
const {
  tasks: allTasks, // 获取所有任务，因为 dashboard 只展示一小部分
  loading: loadingTasks,
  error: tasksError,
  getTaskStatusText, // 引入 getter
  getTaskStatusClass // 引入 getter
} = storeToRefs(taskStore);


const 修行时长 = computed(() => {
  // Ensure todayHours.value is a number, default to 0 if not
  const currentTodayHours = typeof todayHours.value === 'number' ? todayHours.value : 0;

  if (clockOutTime.value && clockInTime.value) { // User has clocked IN and OUT for the day
    // Display the total hours for the day from the store
    const hours = Math.floor(currentTodayHours);
    const minutes = Math.round((currentTodayHours - hours) * 60);
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
  }

  // If clocked IN, but not clocked OUT yet, calculate ongoing time
  if (clockInTime.value && typeof clockInTime.value === 'string' && clockInTime.value.includes(':')) {
    const [startHours, startMinutes] = clockInTime.value.split(':').map(Number);
    const startTime = new Date();
    startTime.setHours(startHours, startMinutes, 0, 0);

    const now = new Date();
    // Ensure startTime is a valid date and now is after startTime
    if (isNaN(startTime.getTime()) || now < startTime) {
      // This case might happen briefly if data is syncing or if clockInTime is from a future time (error)
      return '计时中...';
    }

    const diffMs = now - startTime;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${String(diffHours).padStart(2, '0')}:${String(diffMinutes).padStart(2, '0')} (进行中)`;
  }

  // Default: Not clocked in, or data not yet available for today's session
  return '00:00';
});


const clockInButtonText = computed(() => {
  if (!clockedIn.value) return '上班打卡';
  if (clockedIn.value && !clockOutTime.value) return '下班打卡';
  return '今日已打卡'; // This state implies both clockInTime and clockOutTime are set for today
});

const canClockIn = computed(() => !clockedIn.value && !isClockingIn.value);
const canClockOut = computed(() => clockedIn.value && !clockOutTime.value && !isClockingIn.value);


const clockAnimationActive = ref(false);

const handleClockAction = async (type) => {
  if (type === 'in' && !canClockIn.value) {
    return;
  }
  if (type === 'out' && !canClockOut.value) {
    return;
  }

  const result = await attendanceStore.performClockIn(type);
  if (result.success) {
    clockAnimationActive.value = true;
    setTimeout(() => {
      clockAnimationActive.value = false;
    }, 600);
    // performClockIn action in store already calls fetchAttendanceData
  } else {
    alert(result.message || '操作失败');
  }
};


// **修改：从 taskStore 中获取待办任务**
const pendingTasks = computed(() => {
  const userId = userInfo.value?._id;
  if (!userId) return []; // If user not logged in, no tasks
  // Filter for tasks assigned to current user and not yet completed/cancelled
  return allTasks.value
    .filter(task => task.assignee?._id === userId && task.status !== 'completed' && task.status !== 'cancelled')
    .slice(0, 3); // 只显示前3个
});

// Map task categories to icons
const getTaskIcon = (category) => {
  switch (category) {
    case 'work': return '💼';
    case 'study': return '📚';
    case 'design': return '🎨';
    case 'development': return '💻';
    case 'document': return '📄';
    case 'other': return '✨';
    default: return '❓';
  }
};

// Mock Data for Announcements (still mock for now)
const announcements = ref([
  { id: 1, content: '武林大会通知：本月十五，华山之巅，不见不散！', type: 'urgent' },
  { id: 2, content: '新秘籍《代码禅》已入库，欢迎借阅。', type: 'info' },
]);

// Fetch data on mount
onMounted(async () => {
  if (!userStore.userInfo && !userStore.loading) {
    await userStore.fetchUserInfo();
  }
  await attendanceStore.fetchAttendanceData();
  // **新增：获取任务列表**
  if (userStore.isLoggedIn) {
     await taskStore.fetchTasks(); // 获取所有任务，dashboard再筛选
  }
});


watch([clockInTime, clockOutTime, todayHours, clockedIn], () => {
  console.log("Dashboard: Attendance data changed in store.");
}, { deep: true });

// **新增：监听 userInfo 变化，重新获取任务**
watch(userInfo, (newVal, oldVal) => {
  if (newVal?._id !== oldVal?._id && newVal?._id) { // User logged in or switched
    taskStore.fetchTasks();
  } else if (!newVal?._id && oldVal?._id) { // User logged out
    taskStore.tasks = []; // Clear tasks on logout
  }
});

const getRealmTitle = (points) => {
  if (points === undefined || points === null) return '江湖小白';
  if (points < 100) return '初学乍练';
  if (points < 500) return '略有小成';
  if (points < 1000) return '驾轻就熟';
  if (points < 2000) return '融会贯通';
  if (points < 5000) return '一代宗师';
  return '深不可测';
};

const 江湖声望 = computed(() => {
    return userInfo.value?.stats?.totalHours || 0;
});

</script>

<template>
  <div class="dashboard-page">
    <div class="dashboard-grid">
      <!-- 今日修行 (打卡) -->
      <div class="card practice-card">
        <h2 class="card-title">今日修行</h2>
        <div class="practice-content">
          <div class="practice-visual">
            <span class="pixel-art-placeholder">🧘</span>
          </div>
          <div v-if="loadingAttendance" class="loading-text">卷宗载入中...</div>
          <div v-else-if="attendanceError" class="error-text">{{ attendanceError }}</div>
          <div v-else class="practice-actions">
            <button
              @click="handleClockAction('in')"
              class="clock-btn clock-in-btn"
              :disabled="!canClockIn"
            >
              ☀️ 早课入定
            </button>
            <button
              @click="handleClockAction('out')"
              class="clock-btn clock-out-btn"
              :disabled="!canClockOut"
            >
              🌙 晚课出定
            </button>
            <div v-if="clockedIn && clockOutTime" class="completed-message">
              今日修行圆满！
            </div>
          </div>
          <div class="practice-duration">
            修行时长：{{ 修行时长 }}
          </div>
          <div v-if="clockAnimationActive" class="clock-animation-feedback">✨🎉</div>
          <router-link to="/attendance" class="more-link mt-3">查看修行日志详情...</router-link>
        </div>
      </div>

      <!-- 功力境界 (个人信息) -->
      <div class="card realm-card">
        <h2 class="card-title">功力境界</h2>
        <div v-if="loadingUser" class="loading-text">侠士信息载入中...</div>
        <div v-else-if="userError" class="error-text">{{ userError }}</div>
        <div v-else-if="userInfo" class="realm-content">
          <div class="realm-profile">
            <img :src="fullAvatarUrl" alt="头像" class="realm-avatar" />
            <div class="realm-user-details">
              <p class="realm-nickname">{{ userInfo.nickname || userInfo.username }}</p>
              <p class="realm-title">{{ userInfo.position || getRealmTitle(userInfo.stats?.points) }}</p>
            </div>
          </div>
          <div class="realm-stats">
            <p>💰 内力值：<span class="number-display">{{ userInfo.stats?.points || 0 }}</span></p>
            <p>📜 江湖声望：<span class="number-display">{{ 江湖声望 }}</span></p>
          </div>
        </div>
        <div v-else class="loading-text">侠士信息载入中或尚未入世。</div>
      </div>

      <!-- 待办榜文 (任务预览) -->
      <div class="card todo-card">
        <h2 class="card-title">待办榜文</h2>
        <div v-if="loadingTasks" class="loading-text">榜文载入中...</div>
        <div v-else-if="tasksError" class="error-text">{{ tasksError }}</div>
        <ul v-else-if="pendingTasks.length > 0" class="todo-list">
          <li v-for="task in pendingTasks" :key="task._id" :class="getTaskStatusClass(task.status)">
            <span>{{ getTaskIcon(task.category) }} {{ task.title }}</span>
            <span class="task-reward-preview">💰 {{ task.rewardPoints }}</span>
            <span :class="getTaskStatusClass(task.status)" class="pixel-seal">{{ getTaskStatusText(task.status) }}</span>
          </li>
        </ul>
        <p v-else class="empty-text">今日无事，可静心参悟。</p>
        <router-link to="/tasks" class="more-link">查看更多榜文...</router-link>
      </div>

      <!-- 武林秘闻 (公告/动态) -->
      <div class="card news-card">
        <h2 class="card-title">武林秘闻</h2>
        <ul v-if="announcements.length > 0" class="news-list">
          <li v-for="newsItem in announcements.slice(0, 2)" :key="newsItem.id" :class="newsItem.type">
            {{ newsItem.content }}
          </li>
        </ul>
        <p v-else class="empty-text">江湖平静，暂无秘闻。</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// ... (styles remain unchanged from your provided version) ...
.dashboard-page {
  padding: 20px;
  background-color: #f0e6d2; // 类似羊皮纸/书卷的浅米色
  font-family: '像素体', 'SimSun', sans-serif; // 优先使用像素字体
  min-height: calc(100vh - 60px); // Assuming header height
  position: relative;
}

.dashboard-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

  @media (min-width: 992px) { // Larger screens, 2x2 layout
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;

    .practice-card { grid-area: 1 / 1 / 2 / 2; }
    .realm-card { grid-area: 1 / 2 / 2 / 3; }
    .todo-card { grid-area: 2 / 1 / 3 / 2; }
    .news-card { grid-area: 2 / 2 / 3 / 3; }
  }
}

.card {
  background-color: #fffaf0; // 象牙白
  border: 2px solid #8b4513; // 棕色边框，像木框
  border-radius: 8px; // 轻微圆角，保持像素感
  padding: 20px;
  box-shadow: 5px 5px 0px #c8ada7; // 像素风格阴影
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1.8em;
  color: #5a3922; // 深棕色
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px dashed #8b4513;
  padding-bottom: 10px;
}

.loading-text, .empty-text, .error-text {
  text-align: center;
  color: #7a5c44; // 中棕色
  padding: 10px 0;
}
.error-text {
  color: #d9534f; // 红色错误提示
}

/* 今日修行 (打卡) */
.practice-card {
  align-items: center;
  .practice-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  .practice-visual {
    font-size: 4em; // 增大图标
    margin-bottom: 20px;
  }
  .practice-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center; /* 垂直居中按钮和消息 */
  }
  .clock-btn {
    padding: 10px 20px;
    font-family: inherit;
    font-size: 1.1em;
    border: 2px solid #8b4513;
    background-color: #f5deb3; // 小麦色
    color: #5a3922;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 3px 3px 0px #c8ada7;
    &:hover:not(:disabled) {
      background-color: #e3cba8;
    }
    &:disabled {
      background-color: #d3d3d3 !important; // 确保禁用样式生效
      color: #777 !important;
      cursor: not-allowed !important;
      box-shadow: none !important;
    }
  }
   .completed-message {
    font-size: 1.1em;
    color: #4caf50; // 绿色
    font-weight: bold;
    // margin-top: 10px; // 如果按钮和它在同一行，则不需要
   }

  .practice-duration {
    font-size: 1.2em;
    color: #5a3922;
    margin-top: 10px;
    font-family: 'Noto Serif SC', serif; /* 使用清晰的字体显示时间 */
    font-weight: bold;
  }
  .clock-animation-feedback {
    font-size: 2em;
    position: absolute;
    animation: pop-fade 0.6s ease-out forwards;
     /* 根据实际布局调整位置，确保动画出现在合适的地方 */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
@keyframes pop-fade {
    0% { transform: scale(0.5); opacity: 0; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 0; }
}


/* 功力境界 (个人信息) */
.realm-card {
  .realm-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  .realm-profile {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  .realm-avatar {
    width: 70px;
    height: 70px;
    border-radius: 0; // 方形像素头像
    border: 2px solid #8b4513;
    object-fit: cover;
    background-color: var(--color-ancient-light-brown); // Placeholder bg
    image-rendering: pixelated; // Ensure pixelation for avatar
  }
  .realm-user-details {
    .realm-nickname {
      font-size: 1.4em;
      font-weight: bold;
      color: #5a3922;
      font-family: 'ZCOOL KuaiLe', serif;
    }
    .realm-title {
      font-size: 1em;
      color: #7a5c44;
      font-family: 'Noto Serif SC', serif;
    }
  }
  .realm-stats {
    font-size: 1.1em;
    color: #5a3922;
    text-align: center; /* 确保统计信息居中 */
    font-family: 'Noto Serif SC', serif; /* 使用清晰的字体显示数字 */
    font-weight: bold;
    p {
      margin: 5px 0;
    }
  }
}

/* 待办榜文 (任务预览) */
.todo-card {
  .todo-list {
    list-style: none;
    padding: 0;
    width: 100%;
    li {
      background-color: var(--color-ancient-paper);
      border: 2px dashed var(--color-ancient-light-brown);
      padding: 10px;
      margin-bottom: 8px;
      border-radius: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1em;
      color: #7a5c44;
      font-family: 'Noto Serif SC', serif;
      gap: 10px;

      &.status-pending {
        border-color: var(--color-ancient-gold);
      }
      &.status-in-progress {
        border-color: var(--color-ancient-jade);
      }
      &.status-completed {
        text-decoration: line-through;
        color: #aaa;
        background-color: var(--color-ancient-stone-gray-light);
        border-color: var(--color-ancient-stone-gray);
      }

      .task-reward-preview {
        font-size: 0.9em;
        color: var(--color-ancient-gold);
        font-weight: bold;
        white-space: nowrap;
      }
    }
  }
  .pixel-seal {
    font-family: 'KaiTi', '楷体', serif;
    font-size: 0.9em;
    color: #d9534f;
    border: 1px solid currentColor;
    padding: 2px 4px;
    border-radius: 2px;
    white-space: nowrap;

    &.status-pending { color: var(--color-ancient-gold); }
    &.status-in-progress { color: var(--color-ancient-jade); }
    &.status-completed { color: #888; }
  }
  .more-link {
    display: block;
    text-align: right;
    margin-top: 10px;
    color: #8b4513;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
}

/* 武林秘闻 (公告/动态) */
.news-card {
  .news-list {
    list-style: none;
    padding: 0;
    width: 100%;
    li {
      background-color: var(--color-ancient-paper); // Paper color
      border-left: 4px solid var(--color-ancient-gold); // Gold left border
      padding: 10px;
      margin-bottom: 8px;
      font-size: 1em;
      color: #7a5c44;
      font-family: 'Noto Serif SC', serif;
      &.urgent {
        border-left-color: var(--color-ancient-blood-red); // Urgent red
        font-weight: bold;
        font-family: 'ZCOOL KuaiLe', serif;
      }
    }
  }
}
</style>