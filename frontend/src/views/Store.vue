<template>
  <div class="store-page">
    <h1 class="page-title">江湖宝库</h1>

    <div class="store-content">

      <div v-if="products.length > 0" class="product-list">
        <div v-for="product in products" :key="product.id" class="card product-card">
          <img :src="product.imageUrl" :alt="product.name" class="product-image">
          <div class="product-details">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">{{ product.price }} 内力值</div>
          </div>
          <button class="buy-button pixel-button primary">兑换</button>
        </div>
      </div>

      <div v-else class="empty-store">
        <p>宝库空空如也，等待秘宝出世...</p>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// Placeholder data for products
const products = ref([
  { id: 1, name: '醒神丹一枚', price: 100, imageUrl: 'https://via.placeholder.com/150/FFD76E/FFFFFF?text=Coffee' }, // 虚拟咖啡一杯
  { id: 2, name: '遁世符（一炷香）', price: 200, imageUrl: 'https://via.placeholder.com/150/AEE5A9/FFFFFF?text=License' }, // 临时摸鱼许可证 (1小时)
  { id: 3, name: '摸鱼秘籍残卷', price: 500, imageUrl: 'https://via.placeholder.com/150/79C7FA/FFFFFF?text=Book' }, // 摸鱼技能书一本
  { id: 4, name: '掌门嘉奖令', price: 999, imageUrl: 'https://via.placeholder.com/150/FFAAA5/FFFFFF?text=Praise' }, // 老板夸夸券
]);

// TODO: Add logic to fetch real products from backend
</script>

<style scoped lang="scss">
.store-page {
  padding: 20px;
  min-height: calc(100vh - 60px); /* Adjust based on header height */
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  background-color: var(--color-ancient-paper); /* Inherited from global style but ensuring it */
}

/* The .ancient-pixel-container class typically applies padding, border, shadow.
   Let's assume the main 'store-page' div implicitly benefits from a larger context
   or will be wrapped in a specific pixel container if needed. */

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
}

.store-content {
  /* Specific styles for store content layout here.
     If the entire content should be inside a "card-like" structure,
     consider wrapping .product-list with an additional div and applying .card to it.
  */
}

.product-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Responsive grid for products */
  gap: 20px; /* Space between product cards */
}

/* .card styles are now global (in style.css) so no need to redefine here.
   It should pick up: background-color: var(--color-ancient-paper);
   border: 3px solid var(--color-ancient-dark-brown);
   box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
   padding: 2em; (from global) */

.product-card {
    display: flex;
    flex-direction: column; /* Stack content vertically */
    align-items: center; /* Center content horizontally */
    text-align: center;
    gap: 15px;
    transition: transform 0.2s ease-in-out;
}

.product-card:hover {
    transform: translateY(-5px); /* Slight lift effect on hover */
}

.product-image {
  width: 100%;
  max-width: 150px;
  height: auto;
  border-radius: 0; /* Sharp corners for pixel image */
  object-fit: cover;
  image-rendering: pixelated; /* Ensure pixelation */
  border: 2px solid var(--color-ancient-dark-brown); /* Pixel border */
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
  text-align: center; /* Ensure text alignment */
}

.product-name {
  font-family: 'Noto Serif SC', serif;
  font-size: 1.1em;
  font-weight: bold;
  color: var(--color-ancient-dark-brown);
}

.product-price {
  font-family: 'Pixelify Sans', monospace;
  font-size: 1.2em;
  color: var(--color-ancient-gold); /* Gold for price */
  font-weight: bold;
}

.buy-button.pixel-button { /* Inherits base pixel-button, then overrides for specific style */
  /* primary class will handle the jade color from ancient-pixel.scss */
  /* No need for explicit background-color/color/hover here unless overriding pixel-button.primary */
  padding: 10px 20px; /* Maintain larger padding for this button */
}


.empty-store {
  text-align: center;
  font-family: 'Noto Serif SC', serif;
  color: var(--color-ancient-light-brown);
  padding: 40px 20px;
  grid-column: 1 / -1; /* Span across all columns in grid */
}

/* Add responsive adjustments later */
@media (max-width: 768px) {
  .store-page {
    padding: 10px;
  }

  .page-title {
    text-align: center; /* Center title on small screens */
  }

  .product-list {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* Adjust grid for smaller screens */
      gap: 15px;
  }

  .product-image {
      max-width: 100px;
  }

  .product-name {
      font-size: 16px;
  }

  .product-price {
      font-size: 18px;
  }

  .buy-button {
      font-size: 14px;
      padding: 8px 15px;
  }
}
</style>