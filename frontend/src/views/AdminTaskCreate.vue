<template>
  <div class="admin-task-create-page ancient-pixel-container">
    <h1 class="page-title">发布榜文 · 掌门任务</h1>

    <div class="card create-task-card">
      <h2 class="card-title">📖 新增江湖榜文</h2>
      <form @submit.prevent="submitTask">
        <div class="form-group">
          <label for="title">榜文标题:</label>
          <input type="text" id="title" v-model="newTask.title" class="pixel-input" required maxlength="100" />
        </div>

        <div class="form-group">
          <label for="description">榜文描述:</label>
          <textarea id="description" v-model="newTask.description" rows="5" class="pixel-input" required maxlength="500"></textarea>
        </div>

        <div class="form-group">
          <label for="rewardPoints">奖励内力值:</label>
          <input type="number" id="rewardPoints" v-model.number="newTask.rewardPoints" class="pixel-input" required min="10" />
        </div>

        <div class="form-group">
          <label for="category">任务分类:</label>
          <select id="category" v-model="newTask.category" class="pixel-input">
            <option value="work">工作 (💼)</option>
            <option value="study">学习 (📚)</option>
            <option value="design">设计 (🎨)</option>
            <option value="development">开发 (💻)</option>
            <option value="document">文档 (📄)</option>
            <option value="other">其他 (✨)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="type">任务类型:</label>
          <select id="type" v-model="newTask.type" class="pixel-input">
            <option value="official">官方榜文</option>
            <option value="personal">个人挑战 (需指定领取者)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="priority">优先级:</label>
          <select id="priority" v-model="newTask.priority" class="pixel-input">
            <option value="low">低</option>
            <option value="medium">中</option>
            <option value="high">高</option>
          </select>
        </div>

        <div class="form-group">
          <label for="dueDate">截止日期:</label>
          <input type="date" id="dueDate" v-model="newTask.dueDate" class="pixel-input" />
        </div>

        <div class="form-group">
          <label class="pixel-checkbox">
            <input type="checkbox" v-model="newTask.isPublic">
            <span>是否公开 (公开榜文所有人可见并可领取)</span>
          </label>
        </div>

        <div v-if="createTaskError" class="error-message shaking-pixel">{{ createTaskError }}</div>
        <div v-if="createTaskSuccess" class="success-message">榜文发布成功！</div>

        <button type="submit" class="pixel-button primary" :disabled="creatingTask">
          {{ creatingTask ? '榜文发布中...' : '🎉 立即发布榜文' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useTaskStore } from '@/stores/task';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';

const taskStore = useTaskStore();
const router = useRouter();

const { creatingTask, createTaskError, createTaskSuccess } = storeToRefs(taskStore);

const newTask = ref({
  title: '',
  description: '',
  rewardPoints: 10,
  category: 'work',
  type: 'official',
  priority: 'medium',
  dueDate: null, // YYYY-MM-DD
  isPublic: true,
  tags: [] // 暂时不提供标签输入，可后续扩展
});

// 重置表单
const resetForm = () => {
  newTask.value = {
    title: '',
    description: '',
    rewardPoints: 10,
    category: 'work',
    type: 'official',
    priority: 'medium',
    dueDate: null,
    isPublic: true,
    tags: []
  };
  taskStore.resetCreateTaskState(); // 清除 store 中的成功/失败状态
};

const submitTask = async () => {
  // 将日期格式化为 ISO 字符串以便后端处理
  const taskDataToSend = { ...newTask.value };
  if (taskDataToSend.dueDate) {
    taskDataToSend.dueDate = new Date(taskDataToSend.dueDate).toISOString();
  }

  const result = await taskStore.createTask(taskDataToSend);
  if (result.success) {
    console.log('榜文发布成功！');
    resetForm(); // 发布成功后重置表单
    // 可以在这里跳转到任务列表，或者在页面上显示成功信息
    // router.push('/tasks');
  } else {
    console.error('榜文发布失败:', result.message);
  }
};

onMounted(() => {
  resetForm(); // 页面加载时重置表单状态
});
</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.admin-task-create-page {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  background-color: var(--color-ancient-paper);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
  align-self: flex-start; // 左对齐
}

.create-task-card {
  @extend .ancient-pixel-container; // 继承通用卡片样式
  width: 100%;
  max-width: 700px; // 适应表单内容
  padding: 30px;

  .card-title {
    font-size: 1.5em;
    color: var(--color-ancient-ink);
    margin-bottom: 25px;
    text-align: center;
    border-bottom: 2px dashed var(--color-ancient-light-brown);
    padding-bottom: 10px;
    font-family: 'ZCOOL KuaiLe', serif;
  }

  .form-group {
    margin-bottom: 20px;
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: var(--color-ancient-dark-brown);
      font-family: 'Pixelify Sans', monospace;
      font-size: 1.1em;
    }
    input[type="text"],
    input[type="number"],
    input[type="date"],
    textarea,
    select {
      @extend .pixel-input; // 继承像素输入框样式
      width: 100%;
    }

    textarea {
      resize: vertical;
    }
    select {
      appearance: none; // 移除默认下拉箭头
      background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20fill%3D%22%23777%22%20d%3D%22M5%206l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E');
      background-repeat: no-repeat;
      background-position: right 10px top 50%;
      background-size: 12px auto;
      padding-right: 30px;
    }
  }

  .pixel-checkbox {
    margin-top: 10px;
    span {
      font-size: 1em;
    }
  }

  .error-message, .success-message {
    @extend .error-message; // 继承样式
    margin-top: 10px;
    margin-bottom: 20px; // 保持与其他表单元素的间距
  }
  .success-message {
    border-color: var(--color-ancient-jade);
    color: var(--color-ancient-jade-dark);
    background-color: var(--color-ancient-paper);
  }

  .pixel-button {
    @extend .pixel-button; // 继承按钮样式
    width: 100%;
    margin-top: 20px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-task-create-page {
    padding: 15px;
  }
  .page-title {
    font-size: 1.8em;
    text-align: center;
    align-self: center;
  }
  .create-task-card {
    padding: 20px;
  }
  .create-task-card .card-title {
    font-size: 1.3em;
  }
  .create-task-card .form-group label {
    font-size: 1em;
  }
  .create-task-card .pixel-input {
    font-size: 0.9em;
  }
  .create-task-card .pixel-button {
    font-size: 0.9em;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .admin-task-create-page {
    padding: 10px;
  }
  .page-title {
    font-size: 1.5em;
    margin-bottom: 15px;
  }
  .create-task-card {
    padding: 15px;
  }
  .create-task-card .card-title {
    font-size: 1.1em;
  }
  .create-task-card .form-group label {
    font-size: 0.9em;
  }
  .create-task-card .pixel-input {
    font-size: 0.85em;
    padding: 8px 10px;
  }
  .create-task-card .pixel-button {
    font-size: 0.8em;
    padding: 8px 15px;
  }
}
</style>
