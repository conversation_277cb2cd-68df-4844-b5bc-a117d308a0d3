<template>
  <div class="community-page ancient-pixel-container">
    <h1>江湖茶馆 · 门派交流</h1>

    <div class="community-content">
      <!-- 版块选择区 (Section A) -->
      <div class="section-selection">
         <h2>探索江湖茶馆</h2>
          <div class="sections-grid">
              <div
                class="section-card card"
                v-for="section in sections"
                :key="section.id"
                :class="{ active: selectedSectionId === section.id }"
                @click="selectSection(section.id)"
              >
                  <span class="section-icon">{{ section.icon }}</span>
                  <p class="section-name">{{ section.name }}</p>
              </div>
          </div>
      </div>

      <!-- 帖子列表区 (Section B) -->
      <div class="post-list-container">
          <h2>{{ selectedSectionName }} 最新动态</h2>
          <div v-if="loadingPosts" class="loading-indicator">卷宗载入中...</div>
          <div v-else-if="posts.length === 0 && !loadingPosts" class="no-posts-message">此茶馆暂无榜文。</div>
          <div v-else class="posts-list">
              <div
                class="post-card card"
                v-for="post in posts"
                :key="post._id"
                @click="viewPostDetail(post._id)"
              >
                  <h3 class="post-title">{{ post.title }}</h3>
                  <!-- Display authorName which should be populated by backend -->
                  <p class="post-meta">
                    执笔人: {{ post.authorName || '无名侠客' }} | 发布时间: {{ new Date(post.timestamp).toLocaleString() }}
                  </p>
                  <!-- <p class="post-summary">{{ post.summary }}</p> --> <!-- Summary might not be needed if showing full content later -->
                  <div class="post-stats">
                      <span>❤️ {{ post.likes }}</span>
                      <span>💬 {{ post.commentsCount }}</span> <!-- Use commentsCount -->
                  </div>
              </div>
          </div>
      </div>
    </div>

    <!-- Floating Action Button for New Post (Section C) - Show only if logged in -->
    <button v-if="isLoggedIn" class="fab-button pixel-button primary" @click="openPostModal">
        📜
    </button>

    <!-- 发布新帖子模态框 -->
    <div v-if="showPostModal" class="modal-overlay">
      <div class="modal-content card">
        <button class="modal-close-button" @click="closePostModal">X</button>
        <h2>发布江湖榜文</h2>
        <form @submit.prevent="submitPost">
          <div class="form-group">
            <label for="post-title">榜文标题:</label>
            <input type="text" id="post-title" v-model="newPost.title" class="pixel-input" required>
          </div>
          <div class="form-group">
            <label for="post-content">榜文内容:</label>
            <textarea id="post-content" v-model="newPost.content" rows="6" class="pixel-input" required></textarea>
          </div>
          <div class="form-group">
            <label for="post-section">所属茶馆:</label>
            <select id="post-section" v-model="newPost.sectionId" class="pixel-input" required>
              <option value="">选择茶馆</option>
              <option v-for="section in sections" :key="section.id" :value="section.id">{{ section.name }}</option>
            </select>
          </div>
          <div v-if="postError" class="error-message">{{ postError }}</div>
          <button
              type="submit"
              class="pixel-button primary"
              :disabled="submittingPost"
          >
             {{ submittingPost ? '榜文发布中...' : '发布榜文' }}
          </button>
        </form>
      </div>
    </div>

    <!-- 帖子详情模态框 -->
    <div v-if="selectedPostId !== null" class="modal-overlay" @click.self="closePostDetailModal">
        <div class="modal-content post-detail-modal card">
             <button class="modal-close-button" @click="closePostDetailModal">X</button>

             <div v-if="loadingPostDetail" class="loading-indicator">卷宗载入中...</div>
             <div v-else-if="postDetail" class="post-detail-content">
                 <h2 class="post-detail-title">{{ postDetail.title }}</h2>
                 <!-- Display authorName for post detail -->
                 <p class="post-detail-meta">
                   执笔人: {{ postDetail.authorName || '无名侠客' }} | 发布时间: {{ new Date(postDetail.timestamp).toLocaleString() }}
                  </p>
                 <div class="post-detail-body" v-html="postDetail.content"></div> <!-- Use v-html if content can have HTML, otherwise {{ postDetail.content }} -->
                  <div class="post-detail-stats post-stats">
                      <span>❤️ {{ postDetail.likes }}</span>
                      <span>💬 {{ postDetail.comments ? postDetail.comments.length : 0 }}</span>
                  </div>

                  <div class="comment-section">
                      <h3>茶馆评语</h3>
                       <div v-if="loadingComments" class="loading-indicator">评语载入中...</div>
                       <div v-else-if="comments.length === 0 && !loadingComments" class="no-comments-message">暂无评语。</div>
                       <div v-else class="comments-list">
                            <div class="comment-item card" v-for="comment in comments" :key="comment._id"> <!-- Use _id -->
                               <!-- Display authorName for comment -->
                               <p class="comment-meta">
                                 评语者: {{ comment.authorName || '无名侠客' }} | 发布时间: {{ new Date(comment.timestamp).toLocaleString() }}
                                </p>
                               <p class="comment-content">{{ comment.content }}</p>
                           </div>
                       </div>

                      <!-- 发表评论表单 - Show only if logged in -->
                      <div v-if="isLoggedIn" class="new-comment-form">
                           <h4>撰写评语</h4>
                           <textarea
                               placeholder="留下您的江湖评语..."
                               rows="3"
                               v-model="newCommentContent"
                               class="pixel-input"
                           ></textarea>
                           <button
                               class="pixel-button primary"
                               @click="submitComment"
                               :disabled="submittingComment || !newCommentContent.trim()"
                           >
                               {{ submittingComment ? '评语撰写中...' : '发表评语' }}
                            </button>
                            <div v-if="commentError" class="error-message">{{ commentError }}</div>
                      </div>
                      <div v-else class="login-prompt-comment">
                          <p>入世修行，方可留下评语。</p>
                      </div>
                  </div>
             </div>
             <div v-else class="no-posts-message">无法加载榜文详情。</div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';
import { useUserStore } from '@/stores/user'; // Import user store

const userStore = useUserStore(); // Initialize user store
const isLoggedIn = computed(() => userStore.isLoggedIn); // Get login status

// ... (sections, selectedSectionId, selectedSectionName remain the same)
const sections = ref([
  { id: 'daily-banter', name: '江湖闲谈 / 茶余饭后', icon: '☕' },
  { id: 'idea-spark', name: '武学奇想 / 秘籍推演', icon: '💡' },
  { id: 'showcase', name: '奇珍异宝 / 摸鱼心得', icon: '🎨' },
  { id: 'help-qa', name: '武林互助 / 拜师求教', icon: '🤝' },
  { id: 'news-learning', name: '江湖传闻 / 悟道打卡', icon: '📰' },
]);
const selectedSectionId = ref(sections.value[0]?.id || null);
const selectedSectionName = computed(() => {
  const selected = sections.value.find(section => section.id === selectedSectionId.value);
  return selected ? `${selected.name} 茶馆` : '全部茶馆';
});


const posts = ref([]);
const loadingPosts = ref(false);

const fetchPostsBySection = async (sectionId) => {
  loadingPosts.value = true;
  posts.value = [];
  console.log('获取', sectionId || '所有', '茶馆的榜文...');
  try {
    const response = await axios.get('/api/community/posts', {
      params: { sectionId: sectionId }
    });
    console.log('榜文列表获取成功:', response.data);
    posts.value = response.data.map(post => ({
        ...post,
        // Backend now provides authorName directly if populated
        // authorName: post.author ? post.author.username : '未知用户'
    }));

  } catch (error) {
    console.error('获取榜文列表失败:', error);
    // TODO: Display error to user
  } finally {
    loadingPosts.value = false;
  }
};

const selectSection = (sectionId) => {
  selectedSectionId.value = sectionId;
  console.log('选中茶馆 ID:', selectedSectionId.value);
  fetchPostsBySection(selectedSectionId.value);
};

onMounted(() => {
   fetchPostsBySection(selectedSectionId.value);
});

const showPostModal = ref(false);
const newPost = ref({
  title: '',
  content: '',
  sectionId: selectedSectionId.value || sections.value[0]?.id, // Default to current or first section
});
const submittingPost = ref(false);
const postError = ref(null);

const openPostModal = () => {
  if (!isLoggedIn.value) {
    alert('请先入世修行，方可发布榜文！'); // Or redirect to login
    // router.push('/login');
    return;
  }
  newPost.value = { // Reset form
    title: '',
    content: '',
    sectionId: selectedSectionId.value || sections.value[0]?.id,
  };
  postError.value = null;
  showPostModal.value = true;
};

const closePostModal = () => {
  showPostModal.value = false;
};

const submitPost = async () => {
  if (!isLoggedIn.value) {
    postError.value = '请先入世修行。';
    return;
  }
  // Client-side validation (can be more robust)
  if (!newPost.value.title.trim() || !newPost.value.content.trim() || !newPost.value.sectionId) {
    postError.value = '榜文标题、内容和所属茶馆不能为空。';
    return;
  }

  submittingPost.value = true;
  postError.value = null;
  try {
    const token = localStorage.getItem('token'); // Get token for authenticated request
    const response = await axios.post('/api/community/posts', newPost.value, {
        headers: { Authorization: `Bearer ${token}` }
    });
    console.log('榜文发布成功:', response.data);
    closePostModal();
    await fetchPostsBySection(selectedSectionId.value); // Refresh post list
  } catch (error) {
    console.error('榜文发布失败:', error);
    postError.value = error.response?.data?.message || '发布失败，请稍后再试。';
  } finally {
    submittingPost.value = false;
  }
};


const selectedPostId = ref(null);
const postDetail = ref(null);
const loadingPostDetail = ref(false);
const comments = ref([]);
const loadingComments = ref(false);
const newCommentContent = ref('');
const submittingComment = ref(false);
const commentError = ref(null);

const fetchPostDetail = async (postId) => {
    loadingPostDetail.value = true;
    postDetail.value = null;
    comments.value = []; // Clear previous comments
    console.log('获取榜文 ID:', postId, '的详情...');
    try {
        const response = await axios.get(`/api/community/posts/${postId}`);
        console.log('榜文详情获取成功:', response.data);
        postDetail.value = response.data;

        // **修改点：** 当获取到帖子详情后，处理评论作者信息
        if (postDetail.value && postDetail.value.comments) {
            comments.value = postDetail.value.comments.map(comment => {
                // 从 comment.author 中提取 username 和 avatar
                const authorName = comment.author ? comment.author.username : '无名侠客';
                // 注意：确保后端 User模型中的 avatar 字段存储的是相对路径，如 '/uploads/avatars/user_avatar.jpg'
                // 如果 avatar 已经是完整 URL，则不需要拼接 API_BASE_URL
                const authorAvatar = comment.author && comment.author.avatar
                                    ? `${import.meta.env.VITE_API_BASE_URL}${comment.author.avatar}`
                                    : null; // 或者一个默认头像URL

                return {
                    ...comment,
                    authorName: authorName,
                    authorAvatar: authorAvatar // 如果需要显示头像
                };
            });
        } else {
            comments.value = [];
        }
    } catch (error) {
        console.error('获取榜文详情失败:', error);
        // TODO: Display error to user
    } finally {
        loadingPostDetail.value = false;
    }
};

const viewPostDetail = (postId) => {
    selectedPostId.value = postId;
    console.log('查看榜文 ID:', selectedPostId.value);
    fetchPostDetail(postId); // This will also fetch comments via the post detail
};

const closePostDetailModal = () => {
    selectedPostId.value = null;
    postDetail.value = null;
    comments.value = [];
    commentError.value = null;
    newCommentContent.value = '';
};


const submitComment = async () => {
    if (!isLoggedIn.value) {
        commentError.value = '请先入世修行，方可撰写评语。';
        return;
    }
    if (!newCommentContent.value.trim()) {
        commentError.value = '评语内容不能为空。';
        return;
    }
    if (!selectedPostId.value) {
        commentError.value = '榜文ID丢失，无法撰写评语。';
        return;
    }

    submittingComment.value = true;
    commentError.value = null;
    try {
        const token = localStorage.getItem('token');
        const response = await axios.post(`/api/community/posts/${selectedPostId.value}/comments`,
            { content: newCommentContent.value },
            { headers: { Authorization: `Bearer ${token}` } }
        );
        console.log('评语发表成功:', response.data);
        newCommentContent.value = ''; // Clear input
        // Add the new comment to the list or refetch
        // To ensure correct population, refetching the post detail (which includes comments) is safer
        await fetchPostDetail(selectedPostId.value);
    } catch (error) {
      console.error('评语发表失败:', error);
      commentError.value = error.response?.data?.message || '发表评语失败，请稍后再试。';
    } finally {
        submittingComment.value = false;
    }
};

// Watch for selectedSectionId changes to pre-fill new post's section
watch(selectedSectionId, (newVal) => {
    if (newPost.value) {
        newPost.value.sectionId = newVal;
    }
});

</script>

<style scoped lang="scss">
// Ensure ancient-pixel.scss variables are available
@use '../styles/ancient-pixel.scss' as ap;

.community-page {
  position: relative;
  min-height: calc(100vh - 60px);
  // Background, padding, border, shadow are handled by .ancient-pixel-container
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
}

h1 {
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
  font-size: 2em;
}

.community-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-selection {
    h2 {
        color: var(--color-ancient-dark-brown);
        margin-bottom: 15px;
        font-size: 1.3em;
    }
}

.sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.section-card {
    text-align: center;
    font-family: 'Pixelify Sans', monospace;
    font-size: 1.1em;
    color: var(--color-ancient-dark-brown);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    border-radius: 0; // Sharp corners
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
       transform: translate(-2px, -2px); // Pixel "lift"
       box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
       background-color: var(--color-ancient-highlight);
    }

    .section-icon {
        font-size: 2em;
        margin-bottom: 10px;
        color: var(--color-ancient-ink); // Icon color
    }

    .section-name {
        margin: 0;
        font-weight: bold;
    }

    &.active { // Active state
        background-color: var(--color-ancient-gold); // Gold for active section
        border-color: var(--color-ancient-dark-brown);
        box-shadow: 4px 4px 0px var(--color-ancient-ink); // Stronger shadow
        transform: translate(0px, 0px); // "Pressed" look
    }
}

.post-list-container {
     h2 {
        color: var(--color-ancient-dark-brown);
        margin-bottom: 15px;
        font-size: 1.3em;
     }
}

.loading-indicator,
.no-posts-message {
    text-align: center;
    font-family: 'Noto Serif SC', serif;
    color: var(--color-ancient-light-brown);
    font-style: italic;
    margin-top: 20px;
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.post-card {
    padding: 15px;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    border-radius: 0;
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
    font-family: 'Noto Serif SC', serif;
    color: var(--color-ancient-dark-brown);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;

    &:hover {
       transform: translate(-3px, -3px);
       box-shadow: 9px 9px 0px var(--color-ancient-light-brown);
       background-color: var(--color-ancient-highlight);
    }

    .post-title {
        font-size: 1.1em;
        font-weight: bold;
        margin-top: 0;
        margin-bottom: 5px;
        color: var(--color-ancient-ink);
        font-family: 'ZCOOL KuaiLe', serif;
    }

    .post-meta {
        font-size: 0.85em;
        color: var(--color-ancient-light-brown);
        margin-bottom: 10px;
        font-family: 'Pixelify Sans', monospace;
    }

    .post-summary { // If you add summary back
        font-size: 0.9em;
        color: #555;
        margin-bottom: 10px;
        white-space: pre-wrap; // To respect newlines in summary
        word-break: break-word;
    }

    .post-stats {
        font-size: 0.8em;
        color: var(--color-ancient-dark-brown);
        font-family: 'Pixelify Sans', monospace;
        span {
            margin-right: 15px;
        }
    }
}

.fab-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 0; // Square
  background-color: var(--color-ancient-gold); // Gold for FAB
  color: var(--color-ancient-ink); // Ink color for icon
  font-size: 28px; // Slightly smaller, still prominent
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s, transform 0.1s, box-shadow 0.2s;
  z-index: 10;

   &.cartoon-button {
        /* inherit base styles */
    }

   &.primary { // Use primary styling for FAB (emerald green)
     background-color: var(--color-ancient-jade);
     color: var(--color-neutral-white);
   }

  &:hover {
    transform: translate(-3px, -3px);
    box-shadow: 9px 9px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-jade-light);
  }
  &:active {
       transform: scale(0.95);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6); // Darker overlay
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.modal-content {
  background-color: var(--color-ancient-paper);
  padding: 30px;
  border-radius: 0;
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  width: 90%;
  max-width: 500px;
  position: relative;
  max-height: 90vh; /* Limit modal height */
  overflow-y: auto; /* Allow scrolling within modal */


  h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--color-ancient-ink);
    font-family: 'ZCOOL KuaiLe', serif;
    text-align: center;
  }

  .modal-close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--color-ancient-dark-brown);
    &:hover {
      color: #555;
    }
  }

  .form-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold; // Ensure font is pixel or ancient
      font-family: 'Pixelify Sans', monospace;
      color: var(--color-ancient-dark-brown);
    }

    // Apply pixel-input to form elements
    input[type="text"], 
    textarea, 
    select {
      // Manually apply pixel-input styles if class isn't added
      width: 100%; // Default size
      border: 2px solid var(--color-ancient-dark-brown);
      border-radius: 0;
      padding: 10px 15px;
      font-size: 1em;
      background-color: var(--color-ancient-paper);
      color: var(--color-ancient-ink);
      font-family: 'Pixelify Sans', monospace;
    }

    textarea {
      resize: vertical;
    }

    select {
         appearance: none;
         background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20fill%3D%22%23777%22%20d%3D%22M5%206l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E');
         background-repeat: no-repeat;
         background-position: right 10px top 50%;
         background-size: 12px auto;
         padding-right: 30px;
         font-family: 'Pixelify Sans', monospace;
    }
    // Focus states for manual inputs
    input:focus, textarea:focus, select:focus {
        outline: none;
        border-color: var(--color-ancient-jade);
        box-shadow: 2px 2px 0px var(--color-ancient-jade);
    }
  }

  .error-message {
    color: var(--color-ancient-blood-red);
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-blood-red);
    padding: 10px;
    border-radius: 0;
    text-align: center;
    font-weight: bold;
  }

  button[type="submit"] {
    display: block;
    width: 100%;
    padding: 12px;
    margin-top: 20px;
    font-size: 1em; // Use global pixel-button size
    font-weight: bold;

     &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
     }
  }
}

.modal-content.post-detail-modal {
     h2.post-detail-title {
         text-align: left;
         margin-bottom: 5px;
         font-family: 'ZCOOL KuaiLe', serif;
         font-size: 1.6em;
         color: #333;
     }

     .post-detail-meta {
         font-size: 0.9em;
         color: #777;
         margin-bottom: 15px;
         border-bottom: 1px dashed var(--color-ancient-light-brown);
         padding-bottom: 10px;
         font-family: 'Pixelify Sans', monospace;
     }

     .post-detail-body {
         margin-bottom: 20px;
         line-height: 1.6;
         color: var(--color-ancient-dark-brown);
         font-family: 'Noto Serif SC', serif;
         white-space: pre-wrap; // Respect newlines from textarea
         word-break: break-word; // Break long words
     }

     .post-detail-stats {
         margin-bottom: 20px;
     }

     .comment-section {
         margin-top: 20px;
         border-top: 1px solid #eee;
         padding-top: 20px;

         h3 {
             font-size: 1.2em;
             font-family: 'ZCOOL KuaiLe', serif;
             color: var(--color-ancient-ink);
             margin-bottom: 15px;
         }

         .comments-list {
             margin-top: 15px;
             display: flex;
             flex-direction: column;
             gap: 10px;
         }

          .no-comments-message {
             text-align: center;
             color: var(--color-ancient-light-brown);
             font-family: 'Noto Serif SC', serif;
             font-style: italic;
             margin-top: 10px;
          }

         .comment-item.card {
             padding: 10px;
             background-color: var(--color-ancient-paper);
             border-radius: 0;
             border: 1px solid var(--color-ancient-light-brown);
             box-shadow: 2px 2px 0px var(--color-ancient-light-brown);

             .comment-meta {
                 font-size: 0.8em;
                 color: var(--color-ancient-light-brown);
                 font-family: 'Pixelify Sans', monospace;
                 margin-bottom: 5px;
             }
             .comment-content {
                 font-size: 0.9em;
                 color: var(--color-ancient-dark-brown);
                 font-family: 'Noto Serif SC', serif;
                 margin: 0;
                 line-height: 1.5;
                 white-space: pre-wrap;
                 word-break: break-word;
             }
         }

         .new-comment-form {
             margin-top: 20px;
             padding-top: 15px;
             border-top: 1px dashed #eee;

             h4 {
                 font-size: 1.1em;
                 color: var(--color-ancient-dark-brown);
                 font-family: 'ZCOOL KuaiLe', serif;
                 margin-bottom: 10px;
             }

             // Using plain textarea, apply pixel-input like styles
             textarea {
                 width: 100%;
                 padding: 10px;
                 border: 1px solid #ccc;
                 border-radius: 5px;
                 font-size: 0.95em;
                 box-sizing: border-box;
                 resize: vertical;
                 margin-bottom: 10px;
                 font-family: 'Pixelify Sans', monospace;
                 background-color: var(--color-ancient-paper);
                 color: var(--color-ancient-ink);

                 &:focus {
                    outline: none;
                    border-color: var(--color-ancient-jade);
                    box-shadow: 2px 2px 0px var(--color-ancient-jade);
                 }
             }

             button {
                 // pixel-button class applied in template
             }

             .error-message {
                color: var(--color-ancient-blood-red);
                background-color: var(--color-ancient-paper);
                border: 2px solid var(--color-ancient-blood-red);
                padding: 8px;
                font-size: 0.9em;
                text-align: center;
                font-weight: bold;
            }
         }
         .login-prompt-comment {
            margin-top: 15px;
            text-align: center;
            color: #777;
            font-size: 0.9em;
            font-family: 'Noto Serif SC', serif;
            a {
                color: #FFD76E; // Theme color
                font-weight: bold;
                text-decoration: none;
                &:hover {
                    text-decoration: underline;
                }
            }
         }
     }
}

@media (max-width: 768px) {
  .community-page {
    padding: 15px;
  }

  h1 {
     font-size: 1.8em;
     text-align: center;
     margin-bottom: 15px;
  }

  .community-content {
      gap: 15px;
  }

   .section-selection h2,
   .post-list-container h2 {
       font-size: 1.2em;
       margin-bottom: 10px;
   }

  .sections-grid {
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
  }

   .section-card {
       padding: 15px;
       font-size: 1em;
   }

   .section-card .section-icon {
       font-size: 1.5em;
       margin-bottom: 5px;
   }

  .fab-button {
      bottom: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      font-size: 24px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  .fab-button:hover {
       transform: scale(1.05);
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.25);
  }

  .modal-content.post-detail-modal {
      h2.post-detail-title {
          font-size: 1.4em;
      }

      .post-detail-meta,
      .post-detail-body,
      .post-detail-stats {
          font-size: 0.9em;
      }

      .comment-section h3 {
          font-size: 1.2em;
      }

      .comment-section .comments-list .comment-item.card {
          padding: 8px;
          .comment-meta {
              font-size: 0.75em;
          }
          .comment-content {
              font-size: 0.85em;
          }
      }

      .comment-section .new-comment-form {
          textarea {
              font-size: 0.9em;
          }
          button {
              padding: 6px 12px;
              font-size: 0.9em;
          }
           .error-message {
              padding: 6px;
              font-size: 0.85em;
              margin-top: 8px;
           }
      }
  }
}

@media (max-width: 480px) {
   .community-page {
      padding: 10px;
   }
   h1 {
      font-size: 1.5em;
      margin-bottom: 10px;
   }
   .community-content {
      gap: 10px;
   }
   .section-selection h2,
   .post-list-container h2 {
       font-size: 1.1em;
       margin-bottom: 8px;
   }
   .sections-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 8px;
   }
   .section-card {
       padding: 10px;
       font-size: 0.9em;
   }
    .section-card .section-icon {
       font-size: 1.2em;
       margin-bottom: 3px;
   }
    .fab-button {
      bottom: 15px;
      right: 15px;
      width: 45px;
      height: 45px;
      font-size: 20px;
      box-shadow: 0 3px 9px rgba(0, 0, 0, 0.2);
  }
   .fab-button:hover {
       transform: scale(1.08);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  }

  .modal-content.post-detail-modal {
      h2.post-detail-title {
          font-size: 1.2em;
      }

      .post-detail-meta,
      .post-detail-body,
      .post-detail-stats {
          font-size: 0.85em;
      }

      .comment-section h3 {
          font-size: 1.1em;
      }

      .comment-section .comments-list .comment-item.card {
          padding: 6px;
          .comment-meta {
              font-size: 0.7em;
          }
          .comment-content {
              font-size: 0.8em;
          }
      }

      .comment-section .new-comment-form {
          textarea {
              font-size: 0.85em;
          }
           button {
              padding: 5px 10px;
              font-size: 0.85em;
          }
           .error-message {
              padding: 5px;
              font-size: 0.8em;
              margin-top: 6px;
           }
       }
  }
}
</style>