import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user'; // 引入 userStore 以获取 token

// 从环境变量获取后端API基地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useAttendanceStore = defineStore('attendance', {
  state: () => ({
    // 打卡状态
    clockedIn: false, // 是否已打上班卡 (对应后端 todayStatus.clockedIn)
    clockInTime: '', // 上班打卡时间 (对应后端 todayStatus.clockInTime)
    clockOutTime: '', // 下班打卡时间 (对应后端 todayStatus.clockOutTime)
    isClockingIn: false, // 是否正在执行打卡操作 (用于禁用按钮)

    todayHours: 0,
    lateCount: 0, // **现在表示本月迟到次数 (monthlyLateCount)**
    leaveCount: 0, // 暂为占位符

    loadingStats: false,
    statsError: null,

    // **新增：月度打卡记录**
    monthlyRecords: [], // 用于存储某个月的所有打卡记录
    loadingMonthlyRecords: false,
    monthlyRecordsError: null,
  }),

  getters: {
    clockStatusText: (state) => {
       if (!state.clockedIn) return '新的一天，元气满满去"入定"咯！';
       if (state.clockedIn && !state.clockOutTime) return '今日修行未完，切勿懈怠 (^-^)/ ';
       if (state.clockInTime && state.clockOutTime) return '恭喜出定！今日修行圆满！';
       return '';
    },
    // **新增：根据状态获取对应颜色和文本**
    getRecordStatusDisplay: () => (status) => {
      switch (status) {
        case 'late':
          return { text: '走火', color: 'var(--color-ancient-blood-red)' }; // 红色
        case 'early':
          return { text: '早退', color: 'orange' }; // 橙色
        case 'makeup':
          return { text: '补签', color: 'var(--color-ancient-jade)' }; // 绿色
        default:
          return { text: '正常', color: 'var(--color-ancient-dark-brown)' }; // 正常色
      }
    }
  },

  actions: {
    // ... (_getCurrentTime 保持不变)
    _getCurrentTime() {
       const now = new Date();
       const hours = String(now.getHours()).padStart(2, '0');
       const minutes = String(now.getMinutes()).padStart(2, '0');
       return `${hours}:${minutes}`;
    },

    async fetchAttendanceData() {
      this.loadingStats = true;
      this.statsError = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.statsError = '侠士尚未登录，无法查看修行日志。';
        this.loadingStats = false;
        this.clockedIn = false;
        this.clockInTime = '';
        this.clockOutTime = '';
        this.todayHours = 0; // !! 清空
        this.lateCount = 0;
        this.leaveCount = 0;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/clockin/summary`, {
           headers: {
              Authorization: `Bearer ${token}`
           }
        });

        if (response.data && response.data.success && response.data.data) {
            const { todayStatus, stats } = response.data.data;
            console.log('Fetched attendance summary:', response.data.data);

            this.clockedIn = todayStatus.clockedIn;
            this.clockInTime = todayStatus.clockInTime;
            this.clockOutTime = todayStatus.clockOutTime;

            this.todayHours = stats.todayHours || 0;
            this.lateCount = stats.monthlyLateCount || 0; // **修改：从 monthlyLateCount 获取**
            this.leaveCount = stats.leaveCount || 0;

        } else {
             this.statsError = response.data.message || '获取修行概览数据失败。';
             console.error('Store: 获取修行概览数据失败:', this.statsError);
             this.resetAttendanceState();
        }

      } catch (error) {
         console.error('Store: 获取修行概览数据错误:', error);
         this.statsError = error.response?.data?.message || '获取修行概览数据错误，请稍后再试。';
         if (error.response && error.response.status === 401) {
             userStore.setToken(null);
             this.statsError = '身份验证失败，请重新登录。';
         }
         this.resetAttendanceState();
      } finally {
         this.loadingStats = false;
      }
    },

    // ... (performClockIn 保持不变, 它内部会调用 fetchAttendanceData 来刷新状态)
    async performClockIn(type) { // type: 'in' 或 'out'
        this.isClockingIn = true;
        this.statsError = null;
        const userStore = useUserStore();
        const token = userStore.token;

        if (!token) {
            this.statsError = '侠士尚未登录，无法进行早晚课。';
            this.isClockingIn = false;
            return { success: false, message: this.statsError };
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/api/clockin`,
              { type: type },
              {
                headers: {
                  Authorization: `Bearer ${token}`
                }
              }
            );

            if (response.data && response.data.success) {
               await this.fetchAttendanceData(); // 等待数据刷新完成
               // 打卡后，刷新当前月的记录，以便日历和历史记录更新
               const now = new Date();
               await this.fetchMonthlyRecords(now.getFullYear(), now.getMonth() + 1);
               return { success: true, message: response.data.message || '早课圆满！' };

            } else {
               this.statsError = response.data.message || '早晚课失败，请稍后再试。';
               console.error('Store: 早晚课失败:', this.statsError);
               return { success: false, message: this.statsError };
            }
        } catch (error) {
            console.error('Store: 早晚课请求失败:', error);
            this.statsError = error.response?.data?.message || '早晚课请求失败，请检查网络或稍后再试。';
             if (error.response && error.response.status === 401) {
                 userStore.setToken(null);
                 this.statsError = '身份验证失败，请重新登录。';
             }
            return { success: false, message: this.statsError };
        } finally {
            this.isClockingIn = false;
        }
    },

    // **新增：获取月度打卡记录**
    async fetchMonthlyRecords(year, month) {
      this.loadingMonthlyRecords = true;
      this.monthlyRecordsError = null;
      this.monthlyRecords = [];

      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.monthlyRecordsError = '侠士尚未登录，无法查看历史卷宗。';
        this.loadingMonthlyRecords = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/clockin`, {
          params: { year, month }, // 传递年月参数
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.success) {
          this.monthlyRecords = response.data.clockIns;
          console.log(`Store: 获取到 ${year}年${month}月 打卡记录:`, this.monthlyRecords.length, '条');
        } else {
          this.monthlyRecordsError = response.data.message || '获取月度打卡记录失败。';
          console.error('Store: 获取月度打卡记录失败:', this.monthlyRecordsError);
        }
      } catch (error) {
        console.error('Store: 获取月度打卡记录请求错误:', error);
        this.monthlyRecordsError = error.response?.data?.message || '获取月度打卡记录错误，请稍后再试。';
        if (error.response && error.response.status === 401) {
             userStore.setToken(null);
        }
      } finally {
        this.loadingMonthlyRecords = false;
      }
    },

    resetAttendanceState() {
      this.clockedIn = false;
      this.clockInTime = '';
      this.clockOutTime = '';
      this.todayHours = 0;
      this.lateCount = 0;
      this.leaveCount = 0;
      this.monthlyRecords = [];
    }
  },
});