import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user'; // 需要用到 userStore 更新积分

// 从环境变量获取后端API基地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [], // 任务列表
    loading: false, // 任务列表加载状态
    error: null,    // 任务列表错误信息
    selectedTask: null, // 当前查看的任务详情
    loadingTaskDetail: false, // 任务详情加载状态
    taskDetailError: null,    // 任务详情错误信息
    submittingAction: false, // 领取/完成任务操作状态
    actionError: null,       // 领取/完成任务错误信息
    actionSuccessMessage: null, // 领取/完成任务成功消息
    // 新增：任务创建相关状态
    creatingTask: false,
    createTaskError: null,
    createTaskSuccess: false,
  }),

  getters: {
    // 简化任务状态显示文本
    getTaskStatusText: () => (status) => {
      switch (status) {
        case 'pending': return '待领取';
        case 'in_progress': return '进行中';
        case 'completed': return '已完成';
        case 'cancelled': return '已取消';
        default: return '未知状态';
      }
    },
    // 根据任务状态获取样式类
    getTaskStatusClass: () => (status) => {
      switch (status) {
        case 'pending': return 'status-pending';
        case 'in_progress': return 'status-in-progress';
        case 'completed': return 'status-completed';
        case 'cancelled': return 'status-cancelled';
        default: return '';
      }
    },
    // 任务操作按钮文本
    getTaskActionButtonText: (state) => (task) => {
      const userStore = useUserStore();
      const userId = userStore.userInfo?._id;

      if (task.status === 'completed') return '已完成';
      if (task.status === 'cancelled') return '已取消';
      if (task.status === 'in_progress' && task.assignee?._id === userId) return '完成榜文';
      if (task.status === 'in_progress' && task.assignee?._id !== userId) return '已被领取';
      if (task.status === 'pending') return '领取榜文';
      return '查看';
    },
    // 任务操作按钮是否禁用
    isTaskActionButtonDisabled: (state) => (task) => {
      const userStore = useUserStore();
      const userId = userStore.userInfo?._id;

      if (!userStore.isLoggedIn) return true; // 未登录则禁用所有操作
      if (state.submittingAction) return true; // 操作进行中
      if (task.status === 'completed' || task.status === 'cancelled') return true; // 已完成或已取消
      if (task.status === 'in_progress' && task.assignee?._id !== userId) return true; // 任务已被他人领取
      return false; // 可领取或可完成
    }
  },

  actions: {
    // 获取任务列表
    async fetchTasks(filters = {}) {
      this.loading = true;
      this.error = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.error = '侠士尚未登录，无法查看榜文。';
        this.loading = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/tasks`, {
          params: filters,
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.tasks = response.data.tasks;
          console.log('Store: 任务列表获取成功:', this.tasks.length, '条');
        } else {
          this.error = response.data.message || '获取任务列表失败。';
          console.error('Store: 获取任务列表失败:', this.error);
        }
      } catch (err) {
        console.error('Store: 获取任务列表请求错误:', err);
        this.error = err.response?.data?.message || '获取任务列表错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
      } finally {
        this.loading = false;
      }
    },

    // 获取单个任务详情
    async fetchTaskById(taskId) {
      this.loadingTaskDetail = true;
      this.taskDetailError = null;
      this.selectedTask = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.taskDetailError = '侠士尚未登录，无法查看榜文详情。';
        this.loadingTaskDetail = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.selectedTask = response.data.task;
          console.log('Store: 任务详情获取成功:', this.selectedTask.title);
        } else {
          this.taskDetailError = response.data.message || '获取任务详情失败。';
          console.error('Store: 获取任务详情失败:', this.taskDetailError);
        }
      } catch (err) {
        console.error('Store: 获取任务详情请求错误:', err);
        this.taskDetailError = err.response?.data?.message || '获取任务详情错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
      } finally {
        this.loadingTaskDetail = false;
      }
    },

    // 领取任务
    async claimTask(taskId) {
      this.submittingAction = true;
      this.actionError = null;
      this.actionSuccessMessage = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.actionError = '侠士尚未登录，无法领取榜文。';
        this.submittingAction = false;
        return { success: false, message: this.actionError };
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/claim`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.actionSuccessMessage = response.data.message;
          // 更新 tasks 列表中的对应任务状态
          const index = this.tasks.findIndex(t => t._id === taskId);
          if (index !== -1) {
            this.tasks[index] = { ...this.tasks[index], ...response.data.task };
          }
          // 如果当前正在查看此任务详情，也更新
          if (this.selectedTask && this.selectedTask._id === taskId) {
            this.selectedTask = { ...this.selectedTask, ...response.data.task };
          }
          console.log('Store: 榜文领取成功:', response.data.message);
          return { success: true, message: this.actionSuccessMessage };
        } else {
          this.actionError = response.data.message || '领取榜文失败。';
          console.error('Store: 领取榜文失败:', this.actionError);
          return { success: false, message: this.actionError };
        }
      } catch (err) {
        console.error('Store: 领取榜文请求错误:', err);
        this.actionError = err.response?.data?.message || '领取榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.actionError };
      } finally {
        this.submittingAction = false;
      }
    },

    // 完成任务
    async completeTask(taskId) {
      this.submittingAction = true;
      this.actionError = null;
      this.actionSuccessMessage = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.actionError = '侠士尚未登录，无法完成榜文。';
        this.submittingAction = false;
        return { success: false, message: this.actionError };
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/complete`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.actionSuccessMessage = response.data.message;
          // 更新 tasks 列表中的对应任务状态
          const index = this.tasks.findIndex(t => t._id === taskId);
          if (index !== -1) {
            this.tasks[index] = { ...this.tasks[index], ...response.data.task };
          }
          // 如果当前正在查看此任务详情，也更新
          if (this.selectedTask && this.selectedTask._id === taskId) {
            this.selectedTask = { ...this.selectedTask, ...response.data.task };
          }
          // 更新用户积分（前端自行同步或重新获取用户信息）
          await userStore.fetchUserInfo(); // 重新获取用户最新信息，包括积分
          console.log('Store: 榜文完成成功:', response.data.message);
          return { success: true, message: this.actionSuccessMessage };
        } else {
          this.actionError = response.data.message || '完成榜文失败。';
          console.error('Store: 完成榜文失败:', this.actionError);
          return { success: false, message: this.actionError };
        }
      } catch (err) {
        console.error('Store: 完成榜文请求错误:', err);
        this.actionError = err.response?.data?.message || '完成榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.actionError };
      } finally {
        this.submittingAction = false;
      }
    },

    // 清除选中的任务详情
    clearSelectedTask() {
      this.selectedTask = null;
      this.taskDetailError = null;
      this.actionError = null;
      this.actionSuccessMessage = null;
    },

    // 新增：创建任务 Action
    async createTask(taskData) {
      this.creatingTask = true;
      this.createTaskError = null;
      this.createTaskSuccess = false;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.createTaskError = '侠士尚未登录，无法发布榜文。';
        this.creatingTask = false;
        return { success: false, message: this.createTaskError };
      }

      // 调试日志：打印发送的数据
      console.log('Store: 准备发送的任务数据:', taskData);
      console.log('Store: rewardPoints 类型:', typeof taskData.rewardPoints, '值:', taskData.rewardPoints);

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks`, taskData, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.createTaskSuccess = true;
          // 调试日志：打印返回的任务数据
          console.log('Store: 服务器返回的任务数据:', response.data.task);
          console.log('Store: 返回的 rewardPoints:', response.data.task.rewardPoints);

          // 成功后可以考虑刷新任务列表
          await this.fetchTasks();
          console.log('Store: 任务创建成功:', response.data.task.title);
          return { success: true, message: response.data.message || '榜文发布成功！' };
        } else {
          this.createTaskError = response.data.message || '发布榜文失败。';
          console.error('Store: 发布榜文失败:', this.createTaskError);
          return { success: false, message: this.createTaskError };
        }
      } catch (err) {
        console.error('Store: 发布榜文请求错误:', err);
        this.createTaskError = err.response?.data?.message || '发布榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.createTaskError };
      } finally {
        this.creatingTask = false;
      }
    },

    // 新增：重置任务创建状态
    resetCreateTaskState() {
      this.createTaskError = null;
      this.createTaskSuccess = false;
    }
  },
});
