import { defineStore } from 'pinia';
import axios from 'axios';

// 从环境变量获取后端API基地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null, // 用户信息对象
    loading: false, // 是否正在加载用户信息
    error: null,    // 加载或操作用户信息时的错误
    token: localStorage.getItem('token') || null, // 添加 token 状态，并尝试从 localStorage 读取
  }),

  getters: {
    // 判断用户是否已登录 (基于 token 或用户信息)
    isLoggedIn: (state) => !!state.token && !!state.userInfo, // 判断 token 和 userInfo 都存在才算登录

    // 获取完整的头像 URL
    fullAvatarUrl: (state) => {
      // 如果 userInfo 或 userInfo.avatar 不存在，使用后端提供的默认头像
      if (!state.userInfo || !state.userInfo.avatar || state.userInfo.avatar === '👨‍💻') { // 检查默认emoji并替换为实际路径
        return `${API_BASE_URL}/uploads/avatars/demo.jpeg`; // **修改为后端默认头像路径**
      }
      // 如果 userInfo.avatar 是完整的 URL，直接使用
      if (state.userInfo.avatar.startsWith('http')) {
        return state.userInfo.avatar;
      }
      // 否则，将后端基地址与相对路径拼接
      return `${API_BASE_URL}${state.userInfo.avatar}`;
    },
  },

  actions: {
    // 设置用户 token
    setToken(token) {
      this.token = token;
      if (token) {
        localStorage.setItem('token', token); // 将 token 保存到 localStorage
      } else {
        localStorage.removeItem('token'); // 如果 token 为空，移除 localStorage 中的 token
      }
      console.log('Store: 设置 token:', this.token);
    },

    // 从后端获取用户数据并更新状态
    async fetchUserInfo() {
      this.loading = true;
      this.error = null;

      // 从 store 的状态中获取 token
      const token = this.token;
      if (!token) {
        this.userInfo = null; // 没有 token 则用户未登录
        this.loading = false;
        return; // 没有 token 就直接返回
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.data && response.data.success) {
          this.userInfo = response.data.user;
          // 在 fetchUserInfo 成功时，也确保 token 状态是正确的 (虽然理论上此时 token 应该已在 store 中)
          // this.setToken(token); // 可选，确保 store 状态一致
          console.log('Store: 成功获取用户数据:', this.userInfo);
        } else {
          // 获取用户信息失败，可能是 token 无效
          this.userInfo = null;
          this.setToken(null); // 清空 store 中的 token
          this.error = response.data.message || '获取用户数据失败';
          console.error('Store: 获取用户数据失败:', response.data.message);
          // 可以考虑在这里跳转到登录页，但这最好在路由守卫或组件中处理
        }
      } catch (err) {
        console.error('Store: 获取用户数据错误:', err);
        this.userInfo = null;
        this.setToken(null); // 清空 store 中的 token
        this.error = err.response?.data?.message || '网络或服务器错误，请稍后再试';
        // 可以在这里跳转到登录页，但这最好在路由守卫或组件中处理
      } finally {
        this.loading = false;
      }
    },

    // 用户登出，清空状态和 token
    logout() {
      this.userInfo = null;
      this.setToken(null); // 清空 store 中的 token
      // TODO: 如果后端有登出 API，调用后端登出 API
      console.log('Store: 侠士已归隐江湖。'); // Changed logout message
      // 登出后通常需要跳转到登录页，这最好在组件中处理
    },

    // 用户登录成功后更新用户信息 (可选，fetchUserInfo 也可以完成此任务)
    setUserInfo(userData) {
      this.userInfo = userData;
      this.error = null;
      this.loading = false;
      console.log('Store: 设置用户数据:', this.userInfo);
      // 在 setUserInfo 时，如果 userInfo 存在，也可以假设 token 是有效的 (可选)
      // 如果你的登录流程是先获取 token 再获取用户信息，这里可以不做处理
      // 如果你的登录流程是 login 接口同时返回 token 和 userInfo，那么在 Login.vue 中同时同时调用 setToken 和 setUserInfo 比较合适
    },

    // 用户头像上传成功后更新用户信息 (特别是头像路径)
    updateAvatar(avatarPath) {
      if (this.userInfo) {
        this.userInfo.avatar = avatarPath;
        console.log('Store: 用户头像已更新:', this.userInfo.avatar);
      }
    },

    // **新增：更新用户资料 Action**
    async updateUserInfo(updates) {
      this.loading = true; // 开启加载状态
      this.error = null; // 清除之前的错误

      const token = this.token;
      if (!token) {
        this.error = '侠士尚未登录，无法更新资料。';
        this.loading = false;
        return { success: false, message: this.error }; // 返回操作结果
      }

      try {
        const response = await axios.put(`${API_BASE_URL}/api/auth/profile`, updates, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.data && response.data.success) {
          this.userInfo = response.data.user; // 更新 store 中的用户信息为后端返回的最新数据
          console.log('Store: 用户资料更新成功:', this.userInfo);
          return { success: true, message: response.data.message };
        } else {
          this.error = response.data.message || '更新用户资料失败。';
          console.error('Store: 更新用户资料失败:', response.data.message);
          return { success: false, message: this.error };
        }
      } catch (err) {
        console.error('Store: 更新用户资料请求错误:', err);
        this.error = err.response?.data?.message || '网络或服务器错误，请稍后再试。';
        // 如果是 401 错误，意味着 token 失效，清空 token
        if (err.response && err.response.status === 401) {
          this.setToken(null);
        }
        return { success: false, message: this.error }; // 返回操作结果
      } finally {
        this.loading = false; // 关闭加载状态
      }
    },
  },
});