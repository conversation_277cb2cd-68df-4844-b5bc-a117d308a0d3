import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url' // 引入 path 相关的模块

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173, // 前端默认端口
    open: false, // 不自动打开浏览器，避免网络问题
    cors: true, // 允许跨域
    proxy: {
      // 启用代理，解决跨域问题
      '/api': {
        target: 'http://127.0.0.1:3001', // 使用 127.0.0.1 而不是 localhost
        changeOrigin: true,
        secure: false,
      }
    }
  },
  resolve: { // 添加 resolve 配置
    alias: { // 添加 alias 别名配置
      '@': fileURLToPath(new URL('./src', import.meta.url)) // 将 @ 别名指向 src 目录
    }
  }
})
